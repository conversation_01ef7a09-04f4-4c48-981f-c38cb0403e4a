import { importPKCS8, exportJWK } from 'jose';
import { readFileSync } from 'fs';

async function generateJwks() {
  const privateKeyPem = readFileSync('private_key_pkcs8.pem', 'utf8');
  const privateKey = await importPKCS8(privateKeyPem, 'RS256', { extractable: true }); // Add extractable: true

  // Export the public key part of the private key as JWK
  const publicKeyJwk = await exportJWK(privateKey);

  const jwks = {
    keys: [{
      ...publicKeyJwk,
      alg: 'RS256', // Algorithm used for signing
      use: 'sig',   // Use for signing
      kid: 'my-auth-key' // Key ID
    }]
  };
  console.log(JSON.stringify(jwks, null, 2));
}

generateJwks().catch(console.error);
