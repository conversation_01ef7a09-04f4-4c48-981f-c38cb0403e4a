# Google Vertex AI Setup Guide

This guide will help you set up Google Vertex AI for image embeddings in your MaoMao admin dashboard.

## Prerequisites

1. Google Cloud Platform account
2. Billing enabled on your GCP project
3. Admin access to create service accounts

## Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note your project ID (you'll need this later)

## Step 2: Enable Required APIs

Enable the following APIs in your GCP project:

```bash
gcloud services enable aiplatform.googleapis.com
gcloud services enable storage.googleapis.com
```

Or enable them through the Console:
1. Go to APIs & Services > Library
2. Search for and enable:
   - Vertex AI API
   - Cloud Storage API

## Step 3: Create a Service Account

1. Go to IAM & Admin > Service Accounts
2. Click "Create Service Account"
3. Fill in the details:
   - Name: `maomao-vertex-ai`
   - Description: `Service account for MaoMao Vertex AI integration`
4. Grant the following roles:
   - `Vertex AI User`
   - `Storage Object Viewer` (if using Cloud Storage for images)
5. Create and download the JSON key file
6. Store the key file securely (never commit to version control)

## Step 4: Set Environment Variables

Add these to your `.env.local` file:

```env
# Google Cloud Configuration
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json
GCP_PROJECT_ID=your-gcp-project-id
GCP_LOCATION=us-central1
```

## Step 5: Install Dependencies

```bash
npm install @google-cloud/aiplatform
```

## Step 6: Test the Integration

The system includes a test function to verify your setup. Once configured, you can test it by:

1. Adding a product with an image
2. The system will automatically generate embeddings
3. Check the Convex dashboard to see if the `imageEmbedding` field is populated

## Pricing Considerations

- Vertex AI Multimodal Embeddings: ~$0.0001 per image
- For 1000 products with images: ~$0.10
- Very cost-effective for the business value provided

## Security Best Practices

1. Never commit service account keys to version control
2. Use environment variables for all sensitive configuration
3. Regularly rotate service account keys
4. Monitor API usage and set up billing alerts

## Troubleshooting

### Common Issues:

1. **Authentication Error**: Verify your service account key path and permissions
2. **API Not Enabled**: Ensure Vertex AI API is enabled in your project
3. **Quota Exceeded**: Check your API quotas in the GCP Console
4. **Region Issues**: Ensure you're using a supported region (us-central1 recommended)

### Testing Commands:

```bash
# Test authentication
gcloud auth application-default print-access-token

# Test API access
gcloud ai models list --region=us-central1
```

## Next Steps

Once configured, the system will:
1. Automatically generate embeddings for new product images
2. Enable image-based product search
3. Power product recommendations
4. Support visual product discovery

The integration is designed to be seamless and requires no additional admin intervention once set up.
