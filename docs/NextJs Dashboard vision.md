# Admin Panel Development Guide & Vision

## 1. Introduction: The Engine of the App

This document provides the technical vision and functional specifications for the administrative panel of our application. While the client-facing app is the bridge to our users, the admin panel is the engine that powers our entire service-first platform. It is the central hub for our team to manage products, fulfill orders, and engage with our users.

Our guiding principle is **Admin First**. A powerful, intuitive, and efficient admin panel is critical to delivering the curated, high-touch experience we promise our "Global Explorer" users. This guide will serve as the primary reference for the development team.

## 2. Technical Architecture

Our stack is chosen for its ability to create a real-time, responsive, and data-driven admin experience:

*   **Backend: Convex**
    *   Convex will serve as our real-time database and backend platform. Its reactive nature is ideal for our order management system, where immediate updates on order statuses are crucial for our team to act upon. We will leverage Convex's serverless functions for business logic, such as processing new products for image search and handling order state transitions. Getting started with Convex and Next.js is a straightforward process.

*   **Frontend: Next.js**
    *   We will use Next.js to build a fast, modern, and highly interactive admin interface. Its component-based architecture will allow us to create a modular and maintainable dashboard. Server-side rendering capabilities can be leveraged for initial data loading, ensuring a quick startup experience for our admin users.

*   **Authentication**
    *   We will use Convex Auth, which integrates seamlessly with both Next.js and Convex, to manage admin user authentication and roles. This will allow us to easily implement role-based access control (RBAC) as our team grows.

## 3. Convex Schema and Data Models

A well-structured database is essential for our data-driven approach. Here is a preliminary outline of our core Convex schemas.

*   **`products` Table:**
    *   `title`: string
    *   `description`: string
    *   `curationNotes`: string (Why this product is a great find)
    *   `supplierId`: string (Relates to a `suppliers` table)
    *   `priceInYuan`: number
    *   `serviceFee`: number
    *   `finalPrice`: number
    *   `tags`: array of strings
    *   `images`: array of image URLs
    *   `imageEmbedding`: vector (For image search functionality)
    *   `stockCount`: number
    *   `status`: string ('active', 'inactive', 'archived')

*   **`orders` Table:**
    *   `userId`: string
    *   `items`: array of objects (product details, quantity)
    *   `status`: string ('new', 'sourcing', 'action_required', 'shipped', 'delivered', 'cancelled')
    *   `trackingNumber`: string
    *   `shippingAddress`: object
    *   `communicationHistory`: array of objects (messages to/from the user)
    *   `issueResolution`: object (e.g., suggested alternatives for out-of-stock items)

*   **`users` Table:**
    *   Managed by our authentication provider (e.g., Clerk) but referenced in other tables.
    *   We will store app-specific user data like order history and preferences.

*   **`groupBuys` Table:**
    *   `productId`: string
    *   `targetTiers`: array of objects (quantity, price)
    *   `currentParticipants`: number
    *   `status`: string ('active', 'completed', 'expired')
    *   `startTime`: timestamp
    *   `endTime`: timestamp

## 4. Admin Dashboard: Core Modules

The admin dashboard will be organized into several key modules, each corresponding to a core business function.

### 4.1. Main Dashboard & Analytics

This will be the landing page for admins, providing a high-level overview of the business.
*   **Key Metrics:** Display real-time data for sales volume, new orders, active users, and inventory levels.
*   **Actionable Insights:** Highlight orders that require immediate attention ("Action Required"), low-stock products, and active group buys nearing their goals.
*   **Data Visualization:** Use charts and graphs to show trends in sales, popular products, and user growth.

### 4.2. Product Management (Pillar 1: Effortless Discovery)

This module is central to our curation-focused strategy and must be highly efficient.

*   **Product Ingestion & Curation:**
    *   A clean and intuitive form for adding new products with fields for rich descriptions, high-quality images, pricing, and our unique "curation notes."
    *   Admins will be able to manage supplier information and product categories.

*   **Image Search Data Ingestion:**
    *   The "Add Product" workflow will have a dedicated image uploader.
    *   **Workflow:**
        1.  Admin uploads product images via the Next.js frontend.
        2.  Images are stored in Convex's file storage.
        3.  A Convex `action` is triggered upon successful upload.
        4.  This action sends the image to a third-party deep learning model (e.g., a service like ResNet) to generate a vector embedding—a numerical representation of the image.
        5.  The resulting vector is saved in the `imageEmbedding` field of the corresponding product document in our Convex database.
    *   This process ensures that every new product with an image is automatically indexed and ready for image search on the client app. The data ingestion pipeline is a crucial step for search functionality.

### 4.3. Order Management (Pillar 2: Collaborative Fulfillment)

This module is our primary tool for building user trust through transparent and proactive fulfillment.

*   **Real-time Order Dashboard:**
    *   A kanban-style or table view of all orders, filterable by status.
    *   Leveraging Convex's real-time capabilities, the dashboard will update automatically as new orders come in or statuses change.
    *   Each order will be expandable to show full details, including user information, product details, and communication history.

*   **"Action Required" Workflow:**
    *   When an admin changes an order status to `action_required` (e.g., due to an item being out of stock), a specific UI will appear.
    *   This UI will allow the admin to quickly search the product database (including by image similarity) to find and suggest suitable alternatives.
    *   Pre-written message templates will be available to ensure clear and consistent communication with the user, which will be sent directly from the admin panel.

### 4.4. Group Buy Management (Pillar 3: Community Value)

This module allows admins to create and manage community-driven purchasing events.

*   **Campaign Creation:** A simple interface to create new group buy campaigns, selecting a product, setting pricing tiers based on the number of participants, and defining the campaign duration.
*   **Live Monitoring:** A dashboard to track the progress of each active group buy, showing the number of participants and the current price tier.
*   **Automated Fulfillment:** Once a campaign ends, the system will automatically process the orders for all successful participants at the final unlocked price.

## 5. Admin Development Principles

1.  **Efficiency is Key:** The admin panel must be optimized for speed and ease of use. Every workflow, from adding a product to resolving an order issue, should require the minimum number of clicks.
2.  **Data Integrity:** Implement robust validation on all data entry forms to ensure the data in our Convex database is clean and reliable from day one.
3.  **Clarity and Simplicity:** The UI should be clean, uncluttered, and present information in a way that is easy to understand at a glance.
4.  **Secure by Default:** Implement role-based access control from the start to ensure that team members only have access to the functionalities relevant to their roles.
5.  **Build for Scale:** As our product catalog and order volume grow, the admin panel must remain performant. Utilize features like pagination for all data tables.