"use client";

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { TrendingUp, Calendar, Users, Target, Plus, X } from "lucide-react";
import { useState, useEffect } from "react";

interface Product {
  _id: string;
  title: string;
  finalPrice: number;
  pricingTiers?: Array<{
    minQuantity: number;
    pricePerUnit: number;
    currency: string;
    description?: string;
  }>;
}

interface GroupBuyFormData {
  productId: string;
  title: string;
  description?: string;
  targetTiers: Array<{
    quantity: number;
    price: number;
    currency: string;
    description?: string;
  }>;
  endTime: number;
  maxParticipants?: number;
  autoComplete: boolean;
}

interface GroupBuyFormProps {
  products: Product[];
  initialData?: Partial<GroupBuyFormData>;
  onSubmit: (data: GroupBuyFormData) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
  errors?: Record<string, string>;
}

export function GroupBuyForm({
  products,
  initialData = {},
  onSubmit,
  onCancel,
  isSubmitting = false,
  errors = {}
}: GroupBuyFormProps) {
  const [formData, setFormData] = useState<GroupBuyFormData>({
    productId: "",
    title: "",
    description: "",
    targetTiers: [],
    endTime: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days from now
    maxParticipants: 1000,
    autoComplete: true,
    ...initialData
  });

  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [durationDays, setDurationDays] = useState(30);

  useEffect(() => {
    if (formData.productId) {
      const product = products.find(p => p._id === formData.productId);
      setSelectedProduct(product || null);
      
      // Auto-populate title and tiers from product
      if (product) {
        setFormData(prev => ({
          ...prev,
          title: prev.title || `Group Buy: ${product.title}`,
          targetTiers: product.pricingTiers?.map(tier => ({
            quantity: tier.minQuantity,
            price: tier.pricePerUnit,
            currency: tier.currency,
            description: tier.description
          })) || []
        }));
      }
    }
  }, [formData.productId, products]);

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateEndTime = (days: number) => {
    setDurationDays(days);
    updateFormData("endTime", Date.now() + (days * 24 * 60 * 60 * 1000));
  };

  const addTier = () => {
    const newTier = {
      quantity: Math.max(...formData.targetTiers.map(t => t.quantity), 0) + 10,
      price: selectedProduct?.finalPrice || 0,
      currency: "CNY",
      description: "pieces"
    };
    
    updateFormData("targetTiers", [...formData.targetTiers, newTier]);
  };

  const removeTier = (index: number) => {
    const updatedTiers = formData.targetTiers.filter((_, i) => i !== index);
    updateFormData("targetTiers", updatedTiers);
  };

  const updateTier = (index: number, field: string, value: any) => {
    const updatedTiers = formData.targetTiers.map((tier, i) => 
      i === index ? { ...tier, [field]: value } : tier
    );
    updateFormData("targetTiers", updatedTiers.sort((a, b) => a.quantity - b.quantity));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Product Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            Product Selection
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Product *</label>
            <Select
              value={formData.productId}
              onValueChange={(value) => updateFormData("productId", value)}
            >
              <SelectTrigger className={errors.productId ? "border-destructive" : ""}>
                <SelectValue placeholder="Choose a product for group buying" />
              </SelectTrigger>
              <SelectContent>
                {products.map((product) => (
                  <SelectItem key={product._id} value={product._id}>
                    <div className="flex items-center justify-between w-full">
                      <span>{product.title}</span>
                      <span className="text-sm text-muted-foreground ml-2">
                        ¥{product.finalPrice}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.productId && (
              <p className="text-sm text-destructive">{errors.productId}</p>
            )}
          </div>

          {selectedProduct && (
            <div className="p-4 bg-muted/30 rounded-lg">
              <h4 className="font-medium mb-2">{selectedProduct.title}</h4>
              <p className="text-sm text-muted-foreground">
                Base Price: ¥{selectedProduct.finalPrice}
              </p>
              {selectedProduct.pricingTiers && selectedProduct.pricingTiers.length > 0 && (
                <p className="text-sm text-muted-foreground">
                  {selectedProduct.pricingTiers.length} pricing tiers available
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Campaign Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Campaign Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Campaign Title *</label>
            <Input
              value={formData.title}
              onChange={(e) => updateFormData("title", e.target.value)}
              placeholder="Enter an attractive campaign title"
              className={errors.title ? "border-destructive" : ""}
            />
            {errors.title && (
              <p className="text-sm text-destructive">{errors.title}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Description</label>
            <textarea
              value={formData.description || ""}
              onChange={(e) => updateFormData("description", e.target.value)}
              placeholder="Describe the group buy benefits and details..."
              className="w-full px-3 py-2 border rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-none border-input"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Duration (Days)</label>
              <Select
                value={durationDays.toString()}
                onValueChange={(value) => updateEndTime(parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">7 days</SelectItem>
                  <SelectItem value="14">14 days</SelectItem>
                  <SelectItem value="30">30 days</SelectItem>
                  <SelectItem value="60">60 days</SelectItem>
                  <SelectItem value="90">90 days</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Ends on: {formatDate(formData.endTime)}
              </p>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Max Participants</label>
              <Input
                type="number"
                value={formData.maxParticipants || ""}
                onChange={(e) => updateFormData("maxParticipants", parseInt(e.target.value) || undefined)}
                placeholder="1000"
                min="1"
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={formData.autoComplete}
              onCheckedChange={(checked) => updateFormData("autoComplete", checked)}
            />
            <label className="text-sm font-medium">Auto-complete when target reached</label>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Tiers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Pricing Tiers
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Define quantity-based pricing to incentivize group participation
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {formData.targetTiers.length > 0 ? (
            <div className="space-y-3">
              {formData.targetTiers.map((tier, index) => (
                <div key={index} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="flex-1 grid grid-cols-4 gap-3">
                    <div className="space-y-1">
                      <label className="text-xs text-muted-foreground">Min Quantity</label>
                      <Input
                        type="number"
                        value={tier.quantity}
                        onChange={(e) => updateTier(index, "quantity", parseInt(e.target.value) || 1)}
                        min="1"
                        className="h-8"
                      />
                    </div>
                    <div className="space-y-1">
                      <label className="text-xs text-muted-foreground">Price per Unit</label>
                      <Input
                        type="number"
                        value={tier.price}
                        onChange={(e) => updateTier(index, "price", parseFloat(e.target.value) || 0)}
                        step="0.01"
                        min="0"
                        className="h-8"
                      />
                    </div>
                    <div className="space-y-1">
                      <label className="text-xs text-muted-foreground">Currency</label>
                      <Select
                        value={tier.currency}
                        onValueChange={(value) => updateTier(index, "currency", value)}
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="CNY">CNY</SelectItem>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-1">
                      <label className="text-xs text-muted-foreground">Description</label>
                      <Input
                        value={tier.description || ""}
                        onChange={(e) => updateTier(index, "description", e.target.value)}
                        placeholder="pieces"
                        className="h-8"
                      />
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeTier(index)}
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">No pricing tiers defined yet.</p>
            </div>
          )}

          <Button
            type="button"
            variant="outline"
            onClick={addTier}
            className="w-full"
            disabled={!selectedProduct}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Pricing Tier
          </Button>
        </CardContent>
      </Card>

      {/* Submit Actions */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting || !formData.productId || formData.targetTiers.length === 0}>
          {isSubmitting ? "Creating..." : "Create Group Buy"}
        </Button>
      </div>
    </form>
  );
}
