"use client";

import React from "react";
import ErrorBoundary from "@/components/ErrorBoundary";

interface AppErrorBoundaryProps {
  children: React.ReactNode;
}

export function AppErrorBoundary({ children }: AppErrorBoundaryProps) {
  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Global error caught by root ErrorBoundary:', error, errorInfo);
        // Here you could also send errors to a logging service
      }}
      enableRetry={true}
      maxRetries={3}
      showErrorDetails={process.env.NODE_ENV === 'development'}
    >
      {children}
    </ErrorBoundary>
  );
}