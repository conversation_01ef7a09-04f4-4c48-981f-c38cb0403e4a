"use client";

import React, { Component, ErrorInfo, ReactNode } from "react";
import { Button } from "@/components/ui/button";
import { AlertTriangle, RefreshCw, Home, Bug } from "lucide-react";

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorType: 'render' | 'hydration' | 'network' | 'unknown';
  retryCount: number;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  enableRetry?: boolean;
  maxRetries?: number;
  showErrorDetails?: boolean;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeouts: NodeJS.Timeout[] = [];

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorType: 'unknown',
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorType: ErrorBoundary.categorizeError(error),
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const errorType = ErrorBoundary.categorizeError(error);

    this.setState({
      error,
      errorInfo,
      errorType,
    });

    // Log error details
    console.error('ErrorBoundary caught an error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorType,
      timestamp: new Date().toISOString(),
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  componentWillUnmount() {
    // Clear any pending retry timeouts
    this.retryTimeouts.forEach(timeout => clearTimeout(timeout));
  }

  private static categorizeError(error: Error): ErrorBoundaryState['errorType'] {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    // Check for hydration mismatch
    if (message.includes('hydration') || message.includes('server') && message.includes('client')) {
      return 'hydration';
    }

    // Check for network errors
    if (message.includes('network') || message.includes('fetch') || message.includes('api')) {
      return 'network';
    }

    // Check for rendering errors
    if (stack.includes('render') || message.includes('render') || message.includes('jsx')) {
      return 'render';
    }

    return 'unknown';
  }

  private getErrorMessage(): string {
    const { errorType } = this.state;

    switch (errorType) {
      case 'hydration':
        return 'There was a mismatch between server and client rendering. This can happen when content changes between rendering on the server and client.';
      case 'network':
        return 'A network error occurred while loading content. Please check your internet connection and try again.';
      case 'render':
        return 'Something went wrong while rendering this content. The application encountered an unexpected error.';
      default:
        return 'An unexpected error occurred. Please try refreshing the page or contact support if the problem persists.';
    }
  }

  private getErrorTitle(): string {
    const { errorType } = this.state;

    switch (errorType) {
      case 'hydration':
        return 'Content Mismatch Error';
      case 'network':
        return 'Network Error';
      case 'render':
        return 'Rendering Error';
      default:
        return 'Application Error';
    }
  }

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    const { retryCount } = this.state;

    if (retryCount >= maxRetries) {
      return;
    }

    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      errorType: 'unknown',
      retryCount: prevState.retryCount + 1,
    }));

    // Add a small delay before retry to prevent rapid retry loops
    const timeout = setTimeout(() => {
      this.forceUpdate();
    }, 100);

    this.retryTimeouts.push(timeout);
  };

  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorType: 'unknown',
      retryCount: 0,
    });
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { enableRetry = true, maxRetries = 3, showErrorDetails = false } = this.props;
      const { retryCount, error, errorInfo } = this.state;
      const canRetry = enableRetry && retryCount < maxRetries;

      return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-card border rounded-lg shadow-lg p-6 text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-destructive/10 rounded-full">
                <AlertTriangle className="w-8 h-8 text-destructive" />
              </div>
            </div>

            <h2 className="text-xl font-semibold text-foreground mb-2">
              {this.getErrorTitle()}
            </h2>

            <p className="text-muted-foreground mb-6 leading-relaxed">
              {this.getErrorMessage()}
            </p>

            {/* Error details for development */}
            {showErrorDetails && error && (
              <div className="mb-6 p-4 bg-muted rounded-md text-left">
                <div className="flex items-center gap-2 mb-2">
                  <Bug className="w-4 h-4 text-muted-foreground" />
                  <span className="text-sm font-medium text-muted-foreground">
                    Error Details
                  </span>
                </div>
                <div className="text-xs font-mono text-muted-foreground bg-background p-2 rounded border overflow-auto max-h-32">
                  {error.message}
                  {errorInfo?.componentStack && (
                    <>
                      {'\n\nComponent Stack:'}
                      {errorInfo.componentStack}
                    </>
                  )}
                </div>
              </div>
            )}

            {/* Retry information */}
            {enableRetry && retryCount > 0 && (
              <div className="mb-4 text-sm text-muted-foreground">
                Retry attempts: {retryCount} / {maxRetries}
              </div>
            )}

            {/* Action buttons */}
            <div className="flex flex-col gap-3">
              {canRetry && (
                <Button onClick={this.handleRetry} className="w-full">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
              )}

              <Button
                onClick={this.handleReset}
                variant="outline"
                className="w-full"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Reset
              </Button>

              <Button
                onClick={this.handleGoHome}
                variant="ghost"
                className="w-full"
              >
                <Home className="w-4 h-4 mr-2" />
                Go Home
              </Button>
            </div>

            {/* Additional help text */}
            <div className="mt-6 pt-4 border-t">
              <p className="text-xs text-muted-foreground">
                If this problem persists, please contact support with details about what you were doing when the error occurred.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;