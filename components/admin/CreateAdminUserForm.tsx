import { useState } from "react";
import type { Id } from "@/convex/_generated/dataModel";
import type { Role } from "@/lib/auth/permissions";

interface CreateAdminUserFormProps {
  onClose: () => void;
  onCreate: (formData: { userId: Id<"users">; role: Role }) => Promise<void>;
  isLoading: boolean;
  error: string;
}

function CreateAdminUserForm({ onClose, onCreate, isLoading, error }: CreateAdminUserFormProps) {
  const [userId, setUserId] = useState<Id<"users"> | "">("");
  const [role, setRole] = useState<Role>("moderator");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (userId) {
      await onCreate({ userId, role });
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Admin User</h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="userId" className="block text-sm font-medium text-gray-700">
                User ID
              </label>
              <input
                type="text"
                id="userId"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                value={userId}
                onChange={(e) => setUserId(e.target.value as Id<"users">)}
                required
              />
            </div>
            <div>
              <label htmlFor="role" className="block text-sm font-medium text-gray-700">
                Role
              </label>
              <select
                id="role"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                value={role}
                onChange={(e) => setRole(e.target.value as Role)}
                required
              >
                <option value="moderator">Moderator</option>
                <option value="admin">Admin</option>
                <option value="super_admin">Super Admin</option>
              </select>
            </div>
            {error && (
              <div className="text-sm text-red-700 bg-red-50 p-2 rounded-md">{error}</div>
            )}
            <div className="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                disabled={isLoading}
              >
                {isLoading ? "Creating..." : "Create Admin"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default CreateAdminUserForm;