"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Building2, Mail, Phone, MapPin, Globe, Star, Clock, Package, CreditCard, Plus, X } from "lucide-react";
import { useState } from "react";

interface SupplierFormData {
  name: string;
  contactInfo: {
    email?: string;
    phone?: string;
    address?: string;
  };
  platformUrl?: string;
  rating?: number;
  notes?: string;
  isActive: boolean;
  platform?: string;
  verificationStatus?: "verified" | "pending" | "unverified";
  businessLicense?: string;
  minimumOrderQuantity?: number;
  leadTime?: {
    min: number;
    max: number;
  };
  shippingRegions: string[];
  paymentMethods: string[];
}

interface SupplierFormProps {
  initialData?: Partial<SupplierFormData>;
  onSubmit: (data: SupplierFormData) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
  errors?: Record<string, string>;
}

export function SupplierForm({
  initialData = {},
  onSubmit,
  onCancel,
  isSubmitting = false,
  errors = {}
}: SupplierFormProps) {
  const [formData, setFormData] = useState<SupplierFormData>({
    name: "",
    contactInfo: {},
    isActive: true,
    platform: "alibaba",
    verificationStatus: "pending",
    shippingRegions: ["Global"],
    paymentMethods: ["T/T"],
    ...initialData
  });

  const [newRegion, setNewRegion] = useState("");
  const [newPaymentMethod, setNewPaymentMethod] = useState("");

  const platforms = [
    { value: "alibaba", label: "Alibaba" },
    { value: "1688", label: "1688.com" },
    { value: "taobao", label: "Taobao" },
    { value: "tmall", label: "Tmall" },
    { value: "dhgate", label: "DHgate" },
    { value: "made-in-china", label: "Made-in-China" },
    { value: "other", label: "Other" }
  ];

  const commonRegions = [
    "Global", "North America", "Europe", "Asia", "China", "USA", "Canada", 
    "UK", "Germany", "France", "Australia", "Japan", "South Korea"
  ];

  const commonPaymentMethods = [
    "T/T", "PayPal", "Credit Card", "Western Union", "MoneyGram", 
    "Alibaba Trade Assurance", "L/C", "Cash"
  ];

  const updateFormData = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof SupplierFormData],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const addShippingRegion = (region: string) => {
    if (region && !formData.shippingRegions.includes(region)) {
      setFormData(prev => ({
        ...prev,
        shippingRegions: [...prev.shippingRegions, region]
      }));
    }
    setNewRegion("");
  };

  const removeShippingRegion = (region: string) => {
    setFormData(prev => ({
      ...prev,
      shippingRegions: prev.shippingRegions.filter(r => r !== region)
    }));
  };

  const addPaymentMethod = (method: string) => {
    if (method && !formData.paymentMethods.includes(method)) {
      setFormData(prev => ({
        ...prev,
        paymentMethods: [...prev.paymentMethods, method]
      }));
    }
    setNewPaymentMethod("");
  };

  const removePaymentMethod = (method: string) => {
    setFormData(prev => ({
      ...prev,
      paymentMethods: prev.paymentMethods.filter(m => m !== method)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Supplier Name *</label>
              <Input
                value={formData.name}
                onChange={(e) => updateFormData("name", e.target.value)}
                placeholder="Enter supplier name"
                className={errors.name ? "border-destructive" : ""}
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Platform</label>
              <Select
                value={formData.platform}
                onValueChange={(value) => updateFormData("platform", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {platforms.map((platform) => (
                    <SelectItem key={platform.value} value={platform.value}>
                      {platform.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Platform URL</label>
            <Input
              value={formData.platformUrl || ""}
              onChange={(e) => updateFormData("platformUrl", e.target.value)}
              placeholder="https://..."
              type="url"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Notes</label>
            <textarea
              value={formData.notes || ""}
              onChange={(e) => updateFormData("notes", e.target.value)}
              placeholder="Additional notes about this supplier..."
              className="w-full px-3 py-2 border rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-none border-input"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="w-5 h-5" />
            Contact Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Email</label>
              <Input
                value={formData.contactInfo.email || ""}
                onChange={(e) => updateFormData("contactInfo.email", e.target.value)}
                placeholder="<EMAIL>"
                type="email"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Phone</label>
              <Input
                value={formData.contactInfo.phone || ""}
                onChange={(e) => updateFormData("contactInfo.phone", e.target.value)}
                placeholder="+86 123 4567 8900"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Address</label>
            <Input
              value={formData.contactInfo.address || ""}
              onChange={(e) => updateFormData("contactInfo.address", e.target.value)}
              placeholder="Full business address"
            />
          </div>
        </CardContent>
      </Card>

      {/* Business Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Business Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Verification Status</label>
              <Select
                value={formData.verificationStatus}
                onValueChange={(value) => updateFormData("verificationStatus", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="unverified">Unverified</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Rating (0-5)</label>
              <Input
                type="number"
                value={formData.rating || ""}
                onChange={(e) => updateFormData("rating", parseFloat(e.target.value) || undefined)}
                placeholder="4.5"
                min="0"
                max="5"
                step="0.1"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Min Order Qty</label>
              <Input
                type="number"
                value={formData.minimumOrderQuantity || ""}
                onChange={(e) => updateFormData("minimumOrderQuantity", parseInt(e.target.value) || undefined)}
                placeholder="100"
                min="1"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Business License</label>
            <Input
              value={formData.businessLicense || ""}
              onChange={(e) => updateFormData("businessLicense", e.target.value)}
              placeholder="License number or registration ID"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Lead Time (Days)</label>
            <div className="grid grid-cols-2 gap-4">
              <Input
                type="number"
                value={formData.leadTime?.min || ""}
                onChange={(e) => updateFormData("leadTime", { ...formData.leadTime, min: parseInt(e.target.value) || 0 })}
                placeholder="Min days"
                min="0"
              />
              <Input
                type="number"
                value={formData.leadTime?.max || ""}
                onChange={(e) => updateFormData("leadTime", { ...formData.leadTime, max: parseInt(e.target.value) || 0 })}
                placeholder="Max days"
                min="0"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Actions */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : "Save Supplier"}
        </Button>
      </div>
    </form>
  );
}
