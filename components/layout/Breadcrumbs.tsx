"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { ChevronRight, Home } from "lucide-react";
import { cn } from "@/lib/utils";

interface BreadcrumbItem {
  label: string;
  href: string;
}

const routeLabels: Record<string, string> = {
  dashboard: "Dashboard",
  products: "Products",
  orders: "Orders",
  users: "Users",
  suppliers: "Suppliers",
  "group-buys": "Group Buys",
  settings: "Settings",
  new: "New",
  edit: "Edit",
};

export function Breadcrumbs() {
  const pathname = usePathname();
  
  // Generate breadcrumb items from pathname
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const segments = pathname.split("/").filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];
    
    // Always start with dashboard
    if (segments.length > 0 && segments[0] === "dashboard") {
      breadcrumbs.push({
        label: "Dashboard",
        href: "/dashboard",
      });
      
      // Add subsequent segments
      let currentPath = "/dashboard";
      for (let i = 1; i < segments.length; i++) {
        const segment = segments[i];
        currentPath += `/${segment}`;
        
        // Skip dynamic route segments (like IDs)
        if (segment.match(/^[a-f0-9]{24}$/)) {
          // This looks like a MongoDB ObjectId, skip it
          continue;
        }
        
        const label = routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);
        breadcrumbs.push({
          label,
          href: currentPath,
        });
      }
    }
    
    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
      <Home className="h-4 w-4" />
      
      {breadcrumbs.map((item, index) => (
        <div key={item.href} className="flex items-center space-x-2">
          {index > 0 && <ChevronRight className="h-4 w-4" />}
          
          {index === breadcrumbs.length - 1 ? (
            // Current page - not clickable
            <span className="font-medium text-gray-900 dark:text-white">
              {item.label}
            </span>
          ) : (
            // Previous pages - clickable
            <Link
              href={item.href}
              className={cn(
                "hover:text-gray-900 dark:hover:text-white transition-colors",
                index === 0 && "font-medium"
              )}
            >
              {item.label}
            </Link>
          )}
        </div>
      ))}
    </nav>
  );
}
