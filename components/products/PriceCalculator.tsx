"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { formatPrice, formatYuanPrice } from "@/app/productRedesignMockData";
import { DollarSign } from "lucide-react";

interface PriceCalculatorProps {
  priceInYuan: number;
  serviceFee: number;
  onPriceChange: (field: "priceInYuan" | "serviceFee", value: number) => void;
}

export function PriceCalculator({ priceInYuan, serviceFee, onPriceChange }: PriceCalculatorProps) {
  // Calculate final price (assuming 1 Yuan = 0.15 USD)
  const finalPrice = priceInYuan * 0.15 + serviceFee;

  return (
    <Card className="border-2 border-dashed border-primary/20 bg-gradient-to-br from-primary/5 to-product-accent/5">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <DollarSign className="h-5 w-5 text-primary" />
          <span>Pricing Calculator</span>
        </CardT<PERSON>le>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Price in Yuan (¥)
            </label>
            <Input
              type="number"
              step="0.01"
              value={priceInYuan || ""}
              onChange={(e) => onPriceChange("priceInYuan", parseFloat(e.target.value) || 0)}
              placeholder="0.00"
              className="text-lg font-semibold"
            />
            <p className="text-xs text-muted-foreground">
              Base cost from supplier
            </p>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Service Fee ($)
            </label>
            <Input
              type="number"
              step="0.01"
              value={serviceFee || ""}
              onChange={(e) => onPriceChange("serviceFee", parseFloat(e.target.value) || 0)}
              placeholder="0.00"
              className="text-lg font-semibold"
            />
            <p className="text-xs text-muted-foreground">
              Processing & handling fee
            </p>
          </div>
        </div>

        <div className="p-6 bg-gradient-to-r from-primary/10 to-product-accent/10 rounded-xl border border-primary/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Final Customer Price</p>
              <p className="text-3xl font-bold text-primary">
                {formatPrice(finalPrice)}
              </p>
            </div>
            <div className="text-right text-sm text-muted-foreground">
              <div>Yuan: {formatYuanPrice(priceInYuan)}</div>
              <div>Service: {formatPrice(serviceFee)}</div>
              <div className="border-t pt-1 mt-1 font-semibold text-foreground">
                Total: {formatPrice(finalPrice)}
              </div>
            </div>
          </div>
        </div>

        <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-lg">
          <p className="font-medium mb-1">Exchange Rate Information:</p>
          <p>• 1 Yuan ≈ 0.15 USD (rate used for calculation)</p>
          <p>• Final price includes currency conversion + service fee</p>
        </div>
      </CardContent>
    </Card>
  );
}