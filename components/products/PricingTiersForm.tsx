"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, X, TrendingDown, Users } from "lucide-react";
import { useState } from "react";

interface PricingTier {
  minQuantity: number;
  pricePerUnit: number;
  currency: string;
  description?: string;
}

interface PricingTiersFormProps {
  pricingTiers: PricingTier[];
  onPricingTiersChange: (tiers: PricingTier[]) => void;
  errors?: Record<string, string>;
}

export function PricingTiersForm({
  pricingTiers,
  onPricingTiersChange,
  errors = {}
}: PricingTiersFormProps) {
  const [newTier, setNewTier] = useState<PricingTier>({
    minQuantity: 1,
    pricePerUnit: 0,
    currency: "CNY",
    description: ""
  });

  const addTier = () => {
    if (newTier.minQuantity > 0 && newTier.pricePerUnit > 0) {
      const updatedTiers = [...pricingTiers, newTier]
        .sort((a, b) => a.minQuantity - b.minQuantity);
      
      onPricingTiersChange(updatedTiers);
      setNewTier({
        minQuantity: Math.max(...updatedTiers.map(t => t.minQuantity)) + 1,
        pricePerUnit: 0,
        currency: "CNY",
        description: ""
      });
    }
  };

  const removeTier = (index: number) => {
    const updatedTiers = pricingTiers.filter((_, i) => i !== index);
    onPricingTiersChange(updatedTiers);
  };

  const updateTier = (index: number, field: keyof PricingTier, value: any) => {
    const updatedTiers = pricingTiers.map((tier, i) => 
      i === index ? { ...tier, [field]: value } : tier
    );
    onPricingTiersChange(updatedTiers.sort((a, b) => a.minQuantity - b.minQuantity));
  };

  const calculateSavings = (tier: PricingTier, baseTier: PricingTier) => {
    if (!baseTier || tier.pricePerUnit >= baseTier.pricePerUnit) return 0;
    return ((baseTier.pricePerUnit - tier.pricePerUnit) / baseTier.pricePerUnit * 100);
  };

  const baseTier = pricingTiers[0];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingDown className="w-5 h-5" />
          Pricing Tiers for Group Buys
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Set quantity-based pricing to enable group buying. Lower prices for higher quantities encourage bulk purchases.
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Existing Tiers */}
        {pricingTiers.length > 0 && (
          <div className="space-y-4">
            <label className="text-sm font-medium">Current Pricing Tiers</label>
            <div className="space-y-3">
              {pricingTiers.map((tier, index) => (
                <div key={index} className="flex items-center gap-4 p-4 border rounded-lg bg-muted/20">
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="w-4 h-4" />
                    <span className="font-medium">{tier.minQuantity}+</span>
                  </div>
                  
                  <div className="flex-1 grid grid-cols-3 gap-3">
                    <div className="space-y-1">
                      <label className="text-xs text-muted-foreground">Min Quantity</label>
                      <Input
                        type="number"
                        value={tier.minQuantity}
                        onChange={(e) => updateTier(index, "minQuantity", parseInt(e.target.value) || 1)}
                        min="1"
                        className="h-8"
                      />
                    </div>
                    
                    <div className="space-y-1">
                      <label className="text-xs text-muted-foreground">Price per Unit</label>
                      <div className="flex gap-1">
                        <Input
                          type="number"
                          value={tier.pricePerUnit}
                          onChange={(e) => updateTier(index, "pricePerUnit", parseFloat(e.target.value) || 0)}
                          step="0.01"
                          min="0"
                          className="h-8"
                        />
                        <Select
                          value={tier.currency}
                          onValueChange={(value) => updateTier(index, "currency", value)}
                        >
                          <SelectTrigger className="w-20 h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="CNY">CNY</SelectItem>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <label className="text-xs text-muted-foreground">Description</label>
                      <Input
                        placeholder="e.g., pieces, sets"
                        value={tier.description || ""}
                        onChange={(e) => updateTier(index, "description", e.target.value)}
                        className="h-8"
                      />
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {baseTier && index > 0 && (
                      <div className="text-xs text-green-600 font-medium">
                        -{calculateSavings(tier, baseTier).toFixed(1)}%
                      </div>
                    )}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeTier(index)}
                      className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Add New Tier */}
        <div className="space-y-4">
          <label className="text-sm font-medium">Add New Pricing Tier</label>
          <div className="flex items-end gap-4 p-4 border-2 border-dashed rounded-lg">
            <div className="space-y-2">
              <label className="text-xs text-muted-foreground">Min Quantity</label>
              <Input
                type="number"
                placeholder="Min qty"
                value={newTier.minQuantity}
                onChange={(e) => setNewTier({ ...newTier, minQuantity: parseInt(e.target.value) || 1 })}
                min="1"
                className="w-24"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-xs text-muted-foreground">Price per Unit</label>
              <Input
                type="number"
                placeholder="Price"
                value={newTier.pricePerUnit || ""}
                onChange={(e) => setNewTier({ ...newTier, pricePerUnit: parseFloat(e.target.value) || 0 })}
                step="0.01"
                min="0"
                className="w-24"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-xs text-muted-foreground">Currency</label>
              <Select
                value={newTier.currency}
                onValueChange={(value) => setNewTier({ ...newTier, currency: value })}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CNY">CNY</SelectItem>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="EUR">EUR</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2 flex-1">
              <label className="text-xs text-muted-foreground">Description</label>
              <Input
                placeholder="e.g., pieces, sets, boxes"
                value={newTier.description || ""}
                onChange={(e) => setNewTier({ ...newTier, description: e.target.value })}
              />
            </div>
            
            <Button
              type="button"
              onClick={addTier}
              disabled={newTier.minQuantity <= 0 || newTier.pricePerUnit <= 0}
              className="mb-0"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Tier
            </Button>
          </div>
          
          {pricingTiers.length === 0 && (
            <p className="text-xs text-muted-foreground">
              Add at least one pricing tier to enable group buying for this product.
            </p>
          )}
        </div>

        {/* Pricing Preview */}
        {pricingTiers.length > 1 && (
          <div className="p-4 bg-muted/30 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Group Buy Preview</h4>
            <div className="text-xs text-muted-foreground space-y-1">
              {pricingTiers.map((tier, index) => (
                <div key={index} className="flex justify-between">
                  <span>Buy {tier.minQuantity}+ units:</span>
                  <span className="font-medium">
                    {tier.pricePerUnit} {tier.currency} each
                    {index > 0 && baseTier && (
                      <span className="text-green-600 ml-2">
                        (Save {calculateSavings(tier, baseTier).toFixed(1)}%)
                      </span>
                    )}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
