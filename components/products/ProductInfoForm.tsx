"use client";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ProductStatus } from "@/app/productRedesignMockData";
import { X, Plus } from "lucide-react";
import { useState } from "react";

interface Supplier {
  _id: string;
  name: string;
  contactInfo: {
    email: string;
    phone: string;
  };
  platformUrl?: string;
  isActive: boolean;
}

interface ProductInfoFormProps {
  title: string;
  description: string;
  curationNotes: string;
  supplierId: string;
  status: ProductStatus;
  stockCount: number;
  suppliers: Supplier[];
  categories?: string[];
  onFieldChange: (field: string, value: any) => void;
  errors?: Record<string, string>;
}

export function ProductInfoForm({
  title,
  description,
  curationNotes,
  supplierId,
  status,
  stockCount,
  suppliers,
  categories = [],
  onFieldChange,
  errors = {}
}: ProductInfoFormProps) {
  const [newCategory, setNewCategory] = useState("");

  const predefinedCategories = [
    "Electronics", "Clothing", "Home & Garden", "Sports & Outdoors",
    "Health & Beauty", "Toys & Games", "Books & Media", "Automotive",
    "Jewelry & Accessories", "Food & Beverages", "Office Supplies", "Others"
  ];

  const addCategory = (category: string) => {
    if (category && !categories.includes(category)) {
      onFieldChange("categories", [...categories, category]);
    }
    setNewCategory("");
  };

  const removeCategory = (categoryToRemove: string) => {
    onFieldChange("categories", categories.filter(cat => cat !== categoryToRemove));
  };
  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Product Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Product Title *
            </label>
            <Input
              value={title}
              onChange={(e) => onFieldChange("title", e.target.value)}
              placeholder="Enter a compelling product title"
              className={errors.title ? "border-destructive" : ""}
            />
            {errors.title && (
              <p className="text-sm text-destructive">{errors.title}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Description *
            </label>
            <textarea
              value={description}
              onChange={(e) => onFieldChange("description", e.target.value)}
              placeholder="Describe the product features, benefits, and specifications"
              className={`w-full px-3 py-2 border rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-none ${
                errors.description ? "border-destructive" : "border-input"
              }`}
              rows={4}
            />
            {errors.description && (
              <p className="text-sm text-destructive">{errors.description}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Curation Notes
            </label>
            <textarea
              value={curationNotes}
              onChange={(e) => onFieldChange("curationNotes", e.target.value)}
              placeholder="Why is this product special? What makes it a great find?"
              className="w-full px-3 py-2 border border-input rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent resize-none"
              rows={3}
            />
            <p className="text-xs text-muted-foreground">
              Share your expertise - what makes this product worth featuring?
            </p>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Supplier *
            </label>
            <Select
              value={supplierId}
              onValueChange={(value) => onFieldChange("supplierId", value)}
            >
              <SelectTrigger className={errors.supplierId ? "border-destructive" : ""}>
                <SelectValue placeholder="Select a supplier" />
              </SelectTrigger>
              <SelectContent>
                {suppliers.map((supplier) => (
                  <SelectItem key={supplier._id} value={supplier._id}>
                    <div className="flex items-center space-x-2">
                      <span>{supplier.name}</span>
                      {!supplier.isActive && (
                        <span className="text-xs text-muted-foreground">(Inactive)</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.supplierId && (
              <p className="text-sm text-destructive">{errors.supplierId}</p>
            )}
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium">
              Categories
            </label>

            {/* Current categories */}
            {categories.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-2">
                {categories.map((category) => (
                  <Badge key={category} variant="secondary" className="flex items-center gap-1">
                    {category}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => removeCategory(category)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            )}

            {/* Add category */}
            <div className="flex gap-2">
              <Select
                value=""
                onValueChange={(value) => addCategory(value)}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {predefinedCategories
                    .filter(cat => !categories.includes(cat))
                    .map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>

              <div className="flex gap-1">
                <Input
                  placeholder="Custom category"
                  value={newCategory}
                  onChange={(e) => setNewCategory(e.target.value)}
                  className="w-32"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addCategory(newCategory);
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => addCategory(newCategory)}
                  disabled={!newCategory.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <p className="text-xs text-muted-foreground">
              Add categories to help customers find your product
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Product Status & Inventory */}
      <Card>
        <CardHeader>
          <CardTitle>Status & Inventory</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium">
                Status
              </label>
              <Select
                value={status}
                onValueChange={(value) => onFieldChange("status", value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ProductStatus.ACTIVE}>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-success"></div>
                      <span>Active</span>
                    </div>
                  </SelectItem>
                  <SelectItem value={ProductStatus.INACTIVE}>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-warning"></div>
                      <span>Inactive</span>
                    </div>
                  </SelectItem>
                  <SelectItem value={ProductStatus.ARCHIVED}>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-muted-foreground"></div>
                      <span>Archived</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium">
                Stock Count
              </label>
              <Input
                type="number"
                value={stockCount || ""}
                onChange={(e) => onFieldChange("stockCount", parseInt(e.target.value) || 0)}
                placeholder="0"
                min="0"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}