"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>ir<PERSON>, CircleAlert, Zap, Loader2 } from "lucide-react";

interface AIStatusIndicatorProps {
  hasEmbedding: boolean;
  isGenerating?: boolean;
  onGenerateEmbedding?: () => void;
  embeddingDimensions?: number;
}

export function AIStatusIndicator({ 
  hasEmbedding, 
  isGenerating = false, 
  onGenerateEmbedding,
  embeddingDimensions 
}: AIStatusIndicatorProps) {
  if (hasEmbedding) {
    return (
      <div className="space-y-3">
        <Badge variant="default" className="bg-success text-success-foreground">
          <CheckCircle className="h-3 w-3 mr-1" />
          AI Ready
        </Badge>
        
        <div className="flex items-center space-x-2 text-success">
          <Zap className="h-4 w-4" />
          <span className="text-sm font-medium">Visual Search Enabled</span>
        </div>
        
        {embeddingDimensions && (
          <div className="text-xs text-muted-foreground">
            Vector dimensions: {embeddingDimensions} | 
            This product can be found through visual similarity search
          </div>
        )}
      </div>
    );
  }

  if (isGenerating) {
    return (
      <div className="space-y-3">
        <Badge variant="secondary" className="bg-info/20 text-info">
          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
          Generating...
        </Badge>
        
        <div className="flex items-center space-x-2 text-info">
          <Zap className="h-4 w-4" />
          <span className="text-sm font-medium">Processing Images</span>
        </div>
        
        <div className="text-xs text-muted-foreground">
          AI is analyzing product images for visual search capabilities
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <Badge variant="outline" className="border-warning text-warning">
        <CircleAlert className="h-3 w-3 mr-1" />
        Embeddings Pending
      </Badge>
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 text-warning">
          <CircleAlert className="h-4 w-4" />
          <span className="text-sm font-medium">Not Available for Image Search</span>
        </div>
        
        {onGenerateEmbedding && (
          <Button
            size="sm"
            variant="outline"
            onClick={onGenerateEmbedding}
            className="border-primary text-primary hover:bg-primary hover:text-primary-foreground"
          >
            <Zap className="h-4 w-4 mr-1" />
            Generate Now
          </Button>
        )}
      </div>
      
      <div className="text-xs text-muted-foreground space-y-1">
        <p>• Upload images to enable visual search</p>
        <p>• Generate embeddings required for AI matching</p>
      </div>
    </div>
  );
}