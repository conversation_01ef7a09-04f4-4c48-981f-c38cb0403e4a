"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Images } from "lucide-react";
import Image from "next/image";
import { AIStatusIndicator } from "./AIStatusIndicator";

interface ProductImageGalleryProps {
  images: string[];
  title: string;
  hasEmbedding: boolean;
  embeddingDimensions?: number;
  onGenerateEmbedding?: () => void;
  isGeneratingEmbedding?: boolean;
}

export function ProductImageGallery({
  images,
  title,
  hasEmbedding,
  embeddingDimensions,
  onGenerateEmbedding,
  isGeneratingEmbedding = false
}: ProductImageGalleryProps) {
  if (images.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Images className="h-5 w-5" />
            <span>Product Images</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-muted-foreground">
            <Images className="h-16 w-16 mx-auto mb-4 opacity-30" />
            <p className="text-lg font-medium mb-2">No images uploaded</p>
            <p className="text-sm">Add images to enable visual search capabilities</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Images className="h-5 w-5" />
          <span>Product Images</span>
          <Badge variant="outline" className="ml-auto">
            {images.length} image{images.length !== 1 ? 's' : ''}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Image Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {images.map((image, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden bg-muted border hover:border-primary/50 transition-colors">
                <Image
                  src={image}
                  alt={`${title} - Image ${index + 1}`}
                  width={200}
                  height={200}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                />
              </div>
              {index === 0 && (
                <div className="absolute top-2 left-2">
                  <Badge variant="default" className="text-xs bg-primary">
                    Primary
                  </Badge>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* AI Status Section */}
        <div className="border-t pt-6">
          <div className="flex items-center space-x-2 mb-4">
            <span className="text-sm font-medium">AI Visual Search</span>
          </div>
          
          <AIStatusIndicator
            hasEmbedding={hasEmbedding}
            isGenerating={isGeneratingEmbedding}
            onGenerateEmbedding={onGenerateEmbedding}
            embeddingDimensions={embeddingDimensions}
          />
        </div>
      </CardContent>
    </Card>
  );
}