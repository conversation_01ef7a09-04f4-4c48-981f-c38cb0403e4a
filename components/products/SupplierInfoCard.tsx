"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Building2, Mail, Phone, ExternalLink } from "lucide-react";

interface Supplier {
  _id: string;
  name: string;
  contactInfo: {
    email: string;
    phone: string;
  };
  platformUrl?: string;
  isActive: boolean;
}

interface SupplierInfoCardProps {
  supplier: Supplier;
}

export function SupplierInfoCard({ supplier }: SupplierInfoCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Building2 className="h-5 w-5" />
          <span>Supplier</span>
          {supplier.isActive && (
            <Badge variant="default" className="bg-success text-success-foreground ml-auto">
              Active
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-semibold text-lg">{supplier.name}</h3>
        </div>

        <div className="space-y-3">
          <div className="flex items-center space-x-3 text-sm">
            <Mail className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <a 
              href={`mailto:${supplier.contactInfo.email}`}
              className="text-primary hover:underline"
            >
              {supplier.contactInfo.email}
            </a>
          </div>

          <div className="flex items-center space-x-3 text-sm">
            <Phone className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <a 
              href={`tel:${supplier.contactInfo.phone}`}
              className="text-primary hover:underline"
            >
              {supplier.contactInfo.phone}
            </a>
          </div>

          {supplier.platformUrl && (
            <div className="flex items-center space-x-3 text-sm">
              <ExternalLink className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <a 
                href={supplier.platformUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:underline"
              >
                View on platform
              </a>
            </div>
          )}
        </div>

        <div className="pt-3 border-t">
          <div className="text-xs text-muted-foreground space-y-1">
            <p><span className="font-medium">Supplier ID:</span> {supplier._id}</p>
            <p><span className="font-medium">Status:</span> {supplier.isActive ? 'Active' : 'Inactive'}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}