"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Plus, X, Package, Star, AlertCircle } from "lucide-react";
import { useState } from "react";

interface ProductVariant {
  variantName: string;
  variantType: string;
  priceInYuan: number;
  serviceFee: number;
  finalPrice: number;
  stockCount: number;
  availableQuantity?: number;
  minQuantity?: number;
  status: "active" | "inactive" | "out_of_stock";
  isDefault: boolean;
  specifications?: {
    weight?: {
      value: number;
      unit: string;
    };
    customAttributes?: Record<string, string>;
  };
  pricingTiers?: Array<{
    minQuantity: number;
    pricePerUnit: number;
    currency: string;
    description?: string;
  }>;
}

interface ProductVariantsFormProps {
  variants: ProductVariant[];
  onVariantsChange: (variants: ProductVariant[]) => void;
  basePrice?: number;
  errors?: Record<string, string>;
}

export function ProductVariantsForm({
  variants,
  onVariantsChange,
  basePrice = 0,
  errors = {}
}: ProductVariantsFormProps) {
  const [newVariant, setNewVariant] = useState<ProductVariant>({
    variantName: "",
    variantType: "Color",
    priceInYuan: basePrice,
    serviceFee: Math.max(basePrice * 0.15, 5),
    finalPrice: basePrice + Math.max(basePrice * 0.15, 5),
    stockCount: 100,
    status: "active",
    isDefault: variants.length === 0,
  });

  const variantTypes = [
    "Color", "Size", "Material", "Style", "Capacity", "Model", "Version", "Other"
  ];

  const addVariant = () => {
    if (newVariant.variantName.trim()) {
      const updatedVariants = [...variants, newVariant];
      onVariantsChange(updatedVariants);
      
      setNewVariant({
        variantName: "",
        variantType: "Color",
        priceInYuan: basePrice,
        serviceFee: Math.max(basePrice * 0.15, 5),
        finalPrice: basePrice + Math.max(basePrice * 0.15, 5),
        stockCount: 100,
        status: "active",
        isDefault: false,
      });
    }
  };

  const removeVariant = (index: number) => {
    const updatedVariants = variants.filter((_, i) => i !== index);
    
    // If we removed the default variant, make the first remaining variant default
    if (variants[index].isDefault && updatedVariants.length > 0) {
      updatedVariants[0].isDefault = true;
    }
    
    onVariantsChange(updatedVariants);
  };

  const updateVariant = (index: number, field: keyof ProductVariant, value: any) => {
    const updatedVariants = variants.map((variant, i) => {
      if (i === index) {
        const updated = { ...variant, [field]: value };
        
        // Recalculate final price if price changes
        if (field === 'priceInYuan') {
          updated.serviceFee = Math.max(value * 0.15, 5);
          updated.finalPrice = value + updated.serviceFee;
        }
        
        // If setting as default, unset others
        if (field === 'isDefault' && value === true) {
          return updated;
        }
        
        return updated;
      } else if (field === 'isDefault' && value === true) {
        // Unset default for other variants
        return { ...variant, isDefault: false };
      }
      
      return variant;
    });
    
    onVariantsChange(updatedVariants);
  };

  const updateNewVariantPrice = (price: number) => {
    const serviceFee = Math.max(price * 0.15, 5);
    setNewVariant(prev => ({
      ...prev,
      priceInYuan: price,
      serviceFee,
      finalPrice: price + serviceFee
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800";
      case "out_of_stock": return "bg-red-100 text-red-800";
      case "inactive": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const groupedVariants = variants.reduce((acc, variant, index) => {
    if (!acc[variant.variantType]) {
      acc[variant.variantType] = [];
    }
    acc[variant.variantType].push({ ...variant, index });
    return acc;
  }, {} as Record<string, Array<ProductVariant & { index: number }>>);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="w-5 h-5" />
          Product Variants
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Define different options for this product (colors, sizes, etc.) with individual pricing and inventory.
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Existing Variants */}
        {variants.length > 0 && (
          <div className="space-y-4">
            <label className="text-sm font-medium">Current Variants</label>
            
            {Object.entries(groupedVariants).map(([type, typeVariants]) => (
              <div key={type} className="space-y-3">
                <h4 className="text-sm font-medium text-muted-foreground">{type}</h4>
                <div className="space-y-2">
                  {typeVariants.map((variant) => (
                    <div key={variant.index} className="flex items-center gap-4 p-4 border rounded-lg bg-muted/20">
                      <div className="flex items-center gap-2">
                        {variant.isDefault && <Star className="w-4 h-4 text-yellow-500 fill-current" />}
                        <Badge className={getStatusColor(variant.status)}>
                          {variant.status}
                        </Badge>
                      </div>
                      
                      <div className="flex-1 grid grid-cols-4 gap-3">
                        <div className="space-y-1">
                          <label className="text-xs text-muted-foreground">Name</label>
                          <Input
                            value={variant.variantName}
                            onChange={(e) => updateVariant(variant.index, "variantName", e.target.value)}
                            className="h-8"
                          />
                        </div>
                        
                        <div className="space-y-1">
                          <label className="text-xs text-muted-foreground">Price (CNY)</label>
                          <Input
                            type="number"
                            value={variant.priceInYuan}
                            onChange={(e) => updateVariant(variant.index, "priceInYuan", parseFloat(e.target.value) || 0)}
                            step="0.01"
                            min="0"
                            className="h-8"
                          />
                        </div>
                        
                        <div className="space-y-1">
                          <label className="text-xs text-muted-foreground">Stock</label>
                          <Input
                            type="number"
                            value={variant.stockCount}
                            onChange={(e) => updateVariant(variant.index, "stockCount", parseInt(e.target.value) || 0)}
                            min="0"
                            className="h-8"
                          />
                        </div>
                        
                        <div className="space-y-1">
                          <label className="text-xs text-muted-foreground">Status</label>
                          <Select
                            value={variant.status}
                            onValueChange={(value) => updateVariant(variant.index, "status", value)}
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="active">Active</SelectItem>
                              <SelectItem value="inactive">Inactive</SelectItem>
                              <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={variant.isDefault}
                            onCheckedChange={(checked) => updateVariant(variant.index, "isDefault", checked)}
                          />
                          <label className="text-xs text-muted-foreground">Default</label>
                        </div>
                        
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeVariant(variant.index)}
                          className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Add New Variant */}
        <div className="space-y-4">
          <label className="text-sm font-medium">Add New Variant</label>
          <div className="p-4 border-2 border-dashed rounded-lg space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Variant Name *</label>
                <Input
                  placeholder="e.g., Red, Large, 32GB"
                  value={newVariant.variantName}
                  onChange={(e) => setNewVariant({ ...newVariant, variantName: e.target.value })}
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Variant Type</label>
                <Select
                  value={newVariant.variantType}
                  onValueChange={(value) => setNewVariant({ ...newVariant, variantType: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {variantTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Price (CNY)</label>
                <Input
                  type="number"
                  value={newVariant.priceInYuan}
                  onChange={(e) => updateNewVariantPrice(parseFloat(e.target.value) || 0)}
                  step="0.01"
                  min="0"
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Stock Count</label>
                <Input
                  type="number"
                  value={newVariant.stockCount}
                  onChange={(e) => setNewVariant({ ...newVariant, stockCount: parseInt(e.target.value) || 0 })}
                  min="0"
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Final Price</label>
                <Input
                  type="number"
                  value={newVariant.finalPrice.toFixed(2)}
                  disabled
                  className="bg-muted"
                />
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={newVariant.isDefault}
                  onCheckedChange={(checked) => setNewVariant({ ...newVariant, isDefault: checked })}
                />
                <label className="text-xs text-muted-foreground">Set as default variant</label>
              </div>
              
              <Button
                type="button"
                onClick={addVariant}
                disabled={!newVariant.variantName.trim()}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Variant
              </Button>
            </div>
          </div>
        </div>

        {variants.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No variants added yet.</p>
            <p className="text-xs">Add variants if this product comes in different options like colors, sizes, or models.</p>
          </div>
        )}

        {variants.length > 0 && !variants.some(v => v.isDefault) && (
          <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertCircle className="w-4 h-4 text-yellow-600" />
            <p className="text-sm text-yellow-800">
              No default variant selected. The first variant will be used as default.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
