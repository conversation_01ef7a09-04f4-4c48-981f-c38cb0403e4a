"use client";

import { useRef, useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Upload, X, CheckCircle, Loader2, Images } from "lucide-react";
import Image from "next/image";
import { ImageUploadState } from "@/app/productRedesignMockData";

interface ImageFile {
  file: File;
  preview: string;
  uploaded: boolean;
  storageId?: string;
  uploadState: ImageUploadState;
}

interface ImageUploadAreaProps {
  imageFiles: ImageFile[];
  onImageFilesChange: (files: ImageFile[]) => void;
  onUploadImage: (index: number) => Promise<void>;
  uploadingImages: Set<number>;
  maxImages?: number;
}

export function ImageUploadArea({ 
  imageFiles, 
  onImageFilesChange, 
  onUploadImage,
  uploadingImages,
  maxImages = 10
}: ImageUploadAreaProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    addFiles(files);
  };

  const addFiles = (files: File[]) => {
    const newImageFiles: ImageFile[] = [];
    
    files.forEach(file => {
      if (file.type.startsWith('image/') && imageFiles.length + newImageFiles.length < maxImages) {
        const preview = URL.createObjectURL(file);
        newImageFiles.push({
          file,
          preview,
          uploaded: false,
          uploadState: ImageUploadState.PENDING
        });
      }
    });

    if (newImageFiles.length > 0) {
      onImageFilesChange([...imageFiles, ...newImageFiles]);
    }

    // Reset the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeImage = (index: number) => {
    const imageFile = imageFiles[index];
    
    // Clean up preview URL
    URL.revokeObjectURL(imageFile.preview);
    
    // Remove from array
    const newImageFiles = imageFiles.filter((_, i) => i !== index);
    onImageFilesChange(newImageFiles);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = Array.from(e.dataTransfer.files);
    addFiles(files);
  };

  const uploadAllPending = async () => {
    const pendingIndexes = imageFiles
      .map((img, index) => ({ img, index }))
      .filter(({ img }) => !img.uploaded)
      .map(({ index }) => index);

    for (const index of pendingIndexes) {
      if (!uploadingImages.has(index)) {
        await onUploadImage(index);
      }
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Images className="h-5 w-5" />
          <span>Product Images</span>
          <Badge variant="outline" className="ml-auto">
            {imageFiles.length}/{maxImages}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Upload Area */}
        <div
          className={`border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200 ${
            dragActive 
              ? "border-primary bg-primary/10" 
              : "border-muted-foreground/25 hover:border-primary/50 hover:bg-primary/5"
          }`}
          onClick={() => fileInputRef.current?.click()}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className={`h-12 w-12 mx-auto mb-4 transition-colors ${
            dragActive ? "text-primary" : "text-muted-foreground"
          }`} />
          <div className="space-y-2">
            <p className="text-lg font-medium">
              {dragActive ? "Drop images here" : "Upload Product Images"}
            </p>
            <p className="text-sm text-muted-foreground">
              Drag & drop or click to browse
            </p>
            <p className="text-xs text-muted-foreground">
              PNG, JPG, WEBP up to 10MB each • Max {maxImages} images
            </p>
          </div>
        </div>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          multiple
          className="hidden"
          onChange={handleImageSelect}
        />

        {/* Image Preview Grid */}
        {imageFiles.length > 0 && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {imageFiles.map((imageFile, index) => (
                <div key={index} className="relative group">
                  <div className="aspect-square rounded-lg overflow-hidden bg-muted border-2 border-transparent group-hover:border-primary/20 transition-colors">
                    <Image
                      src={imageFile.preview}
                      alt={`Product image ${index + 1}`}
                      width={200}
                      height={200}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  {/* Primary Badge */}
                  {index === 0 && (
                    <div className="absolute top-2 left-2">
                      <Badge variant="default" className="text-xs bg-primary">
                        Primary
                      </Badge>
                    </div>
                  )}

                  {/* Upload Status Overlay */}
                  <div className="absolute inset-0 bg-black/60 flex items-center justify-center rounded-lg opacity-0 group-hover:opacity-100 transition-opacity">
                    {uploadingImages.has(index) ? (
                      <div className="text-center text-white">
                        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                        <p className="text-sm">Uploading...</p>
                      </div>
                    ) : imageFile.uploaded ? (
                      <div className="text-center text-white">
                        <div className="w-8 h-8 bg-success rounded-full flex items-center justify-center mx-auto mb-2">
                          <CheckCircle className="w-5 h-5" />
                        </div>
                        <p className="text-sm">Uploaded</p>
                      </div>
                    ) : (
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onUploadImage(index);
                        }}
                        className="bg-primary hover:bg-primary/90"
                      >
                        <Upload className="h-4 w-4 mr-1" />
                        Upload
                      </Button>
                    )}
                  </div>

                  {/* Remove Button */}
                  <Button
                    size="sm"
                    variant="destructive"
                    className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeImage(index);
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>

            {/* Upload All Button */}
            {imageFiles.some(img => !img.uploaded) && (
              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={uploadAllPending}
                disabled={uploadingImages.size > 0}
              >
                {uploadingImages.size > 0 ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Uploading {uploadingImages.size} image{uploadingImages.size > 1 ? 's' : ''}...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload All Pending Images
                  </>
                )}
              </Button>
            )}
          </div>
        )}

        {/* Guidelines */}
        <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-lg">
          <p className="font-medium mb-1">Image Guidelines:</p>
          <p>• First image will be used as the primary product image</p>
          <p>• Use high-quality images with good lighting</p>
          <p>• Show different angles and use cases</p>
          <p>• Images enable AI-powered visual search</p>
        </div>
      </CardContent>
    </Card>
  );
}