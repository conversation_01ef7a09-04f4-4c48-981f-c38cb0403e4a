"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, X, Palette, Settings } from "lucide-react";
import { useState } from "react";

interface CustomizationOption {
  name: string;
  type: string;
  additionalCost?: number;
  minQuantity?: number;
  description?: string;
}

interface CustomizationOptionsFormProps {
  customizationOptions: CustomizationOption[];
  onCustomizationOptionsChange: (options: CustomizationOption[]) => void;
  errors?: Record<string, string>;
}

export function CustomizationOptionsForm({
  customizationOptions,
  onCustomizationOptionsChange,
  errors = {}
}: CustomizationOptionsFormProps) {
  const [newOption, setNewOption] = useState<CustomizationOption>({
    name: "",
    type: "logo",
    additionalCost: 0,
    minQuantity: 1,
    description: ""
  });

  const customizationTypes = [
    { value: "logo", label: "Logo/Branding" },
    { value: "color", label: "Color Customization" },
    { value: "size", label: "Size Modification" },
    { value: "engraving", label: "Engraving" },
    { value: "printing", label: "Printing" },
    { value: "packaging", label: "Custom Packaging" },
    { value: "material", label: "Material Change" },
    { value: "other", label: "Other" }
  ];

  const addOption = () => {
    if (newOption.name.trim()) {
      onCustomizationOptionsChange([...customizationOptions, newOption]);
      setNewOption({
        name: "",
        type: "logo",
        additionalCost: 0,
        minQuantity: 1,
        description: ""
      });
    }
  };

  const removeOption = (index: number) => {
    const updatedOptions = customizationOptions.filter((_, i) => i !== index);
    onCustomizationOptionsChange(updatedOptions);
  };

  const updateOption = (index: number, field: keyof CustomizationOption, value: any) => {
    const updatedOptions = customizationOptions.map((option, i) => 
      i === index ? { ...option, [field]: value } : option
    );
    onCustomizationOptionsChange(updatedOptions);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="w-5 h-5" />
          Customization Options
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Define available customization services for this product. These will be offered to customers during checkout.
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Existing Options */}
        {customizationOptions.length > 0 && (
          <div className="space-y-4">
            <label className="text-sm font-medium">Available Customizations</label>
            <div className="space-y-3">
              {customizationOptions.map((option, index) => (
                <div key={index} className="flex items-start gap-4 p-4 border rounded-lg bg-muted/20">
                  <div className="flex items-center gap-2 mt-2">
                    <Settings className="w-4 h-4" />
                  </div>
                  
                  <div className="flex-1 grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="space-y-1">
                        <label className="text-xs text-muted-foreground">Service Name</label>
                        <Input
                          placeholder="e.g., Custom Logo"
                          value={option.name}
                          onChange={(e) => updateOption(index, "name", e.target.value)}
                          className="h-8"
                        />
                      </div>
                      
                      <div className="space-y-1">
                        <label className="text-xs text-muted-foreground">Type</label>
                        <Select
                          value={option.type}
                          onValueChange={(value) => updateOption(index, "type", value)}
                        >
                          <SelectTrigger className="h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {customizationTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="space-y-1">
                        <label className="text-xs text-muted-foreground">Additional Cost (CNY)</label>
                        <Input
                          type="number"
                          placeholder="0.00"
                          value={option.additionalCost || ""}
                          onChange={(e) => updateOption(index, "additionalCost", parseFloat(e.target.value) || 0)}
                          step="0.01"
                          min="0"
                          className="h-8"
                        />
                      </div>
                      
                      <div className="space-y-1">
                        <label className="text-xs text-muted-foreground">Min Quantity</label>
                        <Input
                          type="number"
                          placeholder="1"
                          value={option.minQuantity || ""}
                          onChange={(e) => updateOption(index, "minQuantity", parseInt(e.target.value) || 1)}
                          min="1"
                          className="h-8"
                        />
                      </div>
                    </div>
                    
                    <div className="col-span-2 space-y-1">
                      <label className="text-xs text-muted-foreground">Description</label>
                      <Input
                        placeholder="Describe the customization service..."
                        value={option.description || ""}
                        onChange={(e) => updateOption(index, "description", e.target.value)}
                        className="h-8"
                      />
                    </div>
                  </div>

                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeOption(index)}
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10 mt-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Add New Option */}
        <div className="space-y-4">
          <label className="text-sm font-medium">Add Customization Option</label>
          <div className="p-4 border-2 border-dashed rounded-lg space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Service Name *</label>
                <Input
                  placeholder="e.g., Custom Logo, Color Change"
                  value={newOption.name}
                  onChange={(e) => setNewOption({ ...newOption, name: e.target.value })}
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Type</label>
                <Select
                  value={newOption.type}
                  onValueChange={(value) => setNewOption({ ...newOption, type: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {customizationTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Additional Cost (CNY)</label>
                <Input
                  type="number"
                  placeholder="0.00"
                  value={newOption.additionalCost || ""}
                  onChange={(e) => setNewOption({ ...newOption, additionalCost: parseFloat(e.target.value) || 0 })}
                  step="0.01"
                  min="0"
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Min Quantity</label>
                <Input
                  type="number"
                  placeholder="1"
                  value={newOption.minQuantity || ""}
                  onChange={(e) => setNewOption({ ...newOption, minQuantity: parseInt(e.target.value) || 1 })}
                  min="1"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-xs text-muted-foreground">Description</label>
              <Input
                placeholder="Describe what this customization includes..."
                value={newOption.description || ""}
                onChange={(e) => setNewOption({ ...newOption, description: e.target.value })}
              />
            </div>
            
            <div className="flex justify-end">
              <Button
                type="button"
                onClick={addOption}
                disabled={!newOption.name.trim()}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Option
              </Button>
            </div>
          </div>
        </div>

        {customizationOptions.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Settings className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No customization options added yet.</p>
            <p className="text-xs">Add customization services to offer personalized products to your customers.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
