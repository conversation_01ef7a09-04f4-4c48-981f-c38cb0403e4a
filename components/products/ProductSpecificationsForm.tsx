"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, X, Package, Ruler, Weight } from "lucide-react";
import { useState } from "react";

interface Specifications {
  weight?: {
    value: number;
    unit: string;
  };
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
    unit: string;
  };
  material?: string;
  color?: string;
  customAttributes: Record<string, string>;
}

interface ProductSpecificationsFormProps {
  specifications: Specifications;
  onSpecificationsChange: (specs: Specifications) => void;
  errors?: Record<string, string>;
}

export function ProductSpecificationsForm({
  specifications,
  onSpecificationsChange,
  errors = {}
}: ProductSpecificationsFormProps) {
  const [newAttribute<PERSON><PERSON>, setNewAttribut<PERSON><PERSON><PERSON>] = useState("");
  const [newAttributeValue, setNewAttributeValue] = useState("");

  const updateSpecifications = (updates: Partial<Specifications>) => {
    onSpecificationsChange({
      ...specifications,
      ...updates
    });
  };

  const updateWeight = (field: string, value: any) => {
    updateSpecifications({
      weight: {
        ...specifications.weight,
        [field]: value
      } as any
    });
  };

  const updateDimensions = (field: string, value: any) => {
    updateSpecifications({
      dimensions: {
        ...specifications.dimensions,
        [field]: value
      } as any
    });
  };

  const addCustomAttribute = () => {
    if (newAttributeKey.trim() && newAttributeValue.trim()) {
      updateSpecifications({
        customAttributes: {
          ...specifications.customAttributes,
          [newAttributeKey.trim()]: newAttributeValue.trim()
        }
      });
      setNewAttributeKey("");
      setNewAttributeValue("");
    }
  };

  const removeCustomAttribute = (key: string) => {
    const { [key]: removed, ...rest } = specifications.customAttributes;
    updateSpecifications({
      customAttributes: rest
    });
  };

  return (
    <div className="space-y-6">
      {/* Weight & Dimensions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            Physical Specifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Weight */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Weight className="w-4 h-4" />
              <label className="text-sm font-medium">Weight</label>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Input
                  type="number"
                  placeholder="Weight value"
                  value={specifications.weight?.value || ""}
                  onChange={(e) => updateWeight("value", parseFloat(e.target.value) || 0)}
                  step="0.01"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Select
                  value={specifications.weight?.unit || "g"}
                  onValueChange={(value) => updateWeight("unit", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Unit" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="g">Grams (g)</SelectItem>
                    <SelectItem value="kg">Kilograms (kg)</SelectItem>
                    <SelectItem value="lb">Pounds (lb)</SelectItem>
                    <SelectItem value="oz">Ounces (oz)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Dimensions */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Ruler className="w-4 h-4" />
              <label className="text-sm font-medium">Dimensions</label>
            </div>
            <div className="grid grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Length</label>
                <Input
                  type="number"
                  placeholder="Length"
                  value={specifications.dimensions?.length || ""}
                  onChange={(e) => updateDimensions("length", parseFloat(e.target.value) || undefined)}
                  step="0.1"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Width</label>
                <Input
                  type="number"
                  placeholder="Width"
                  value={specifications.dimensions?.width || ""}
                  onChange={(e) => updateDimensions("width", parseFloat(e.target.value) || undefined)}
                  step="0.1"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Height</label>
                <Input
                  type="number"
                  placeholder="Height"
                  value={specifications.dimensions?.height || ""}
                  onChange={(e) => updateDimensions("height", parseFloat(e.target.value) || undefined)}
                  step="0.1"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <label className="text-xs text-muted-foreground">Unit</label>
                <Select
                  value={specifications.dimensions?.unit || "cm"}
                  onValueChange={(value) => updateDimensions("unit", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cm">Centimeters</SelectItem>
                    <SelectItem value="mm">Millimeters</SelectItem>
                    <SelectItem value="in">Inches</SelectItem>
                    <SelectItem value="ft">Feet</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Material & Color */}
      <Card>
        <CardHeader>
          <CardTitle>Material & Appearance</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Material</label>
              <Input
                placeholder="e.g., Cotton, Plastic, Metal"
                value={specifications.material || ""}
                onChange={(e) => updateSpecifications({ material: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Color</label>
              <Input
                placeholder="e.g., Red, Blue, Multi-color"
                value={specifications.color || ""}
                onChange={(e) => updateSpecifications({ color: e.target.value })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Custom Attributes */}
      <Card>
        <CardHeader>
          <CardTitle>Custom Attributes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Existing attributes */}
          {Object.entries(specifications.customAttributes).length > 0 && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Current Attributes</label>
              <div className="flex flex-wrap gap-2">
                {Object.entries(specifications.customAttributes).map(([key, value]) => (
                  <Badge key={key} variant="secondary" className="flex items-center gap-1">
                    <span className="font-medium">{key}:</span>
                    <span>{value}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => removeCustomAttribute(key)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Add new attribute */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Add Custom Attribute</label>
            <div className="flex gap-2">
              <Input
                placeholder="Attribute name"
                value={newAttributeKey}
                onChange={(e) => setNewAttributeKey(e.target.value)}
                className="flex-1"
              />
              <Input
                placeholder="Attribute value"
                value={newAttributeValue}
                onChange={(e) => setNewAttributeValue(e.target.value)}
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addCustomAttribute}
                disabled={!newAttributeKey.trim() || !newAttributeValue.trim()}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Add custom specifications like "Brand", "Model", "Warranty", etc.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
