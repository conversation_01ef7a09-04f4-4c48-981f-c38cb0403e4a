"use client";

import React, { useState, useRef } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Upload,
  Image as ImageIcon,
  X,
  Loader2,
  Eye,
  Sparkles
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface VisualSearchWidgetProps {
  onResultsChange?: (results: any[]) => void;
  maxResults?: number;
  threshold?: number;
  className?: string;
}

export default function VisualSearchWidget({ 
  onResultsChange,
  maxResults = 6,
  threshold = 0.6,
  className = ""
}: VisualSearchWidgetProps) {
  const [searchImage, setSearchImage] = useState<File | null>(null);
  const [searchImagePreview, setSearchImagePreview] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [searchEmbedding, setSearchEmbedding] = useState<number[] | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const generateEmbedding = useAction(api.embeddings.generateImageEmbedding);
  
  // Query for similar products when we have an embedding
  const similarProducts = useQuery(
    api.products.searchProductsByImage,
    searchEmbedding ? {
      targetEmbedding: searchEmbedding,
      limit: maxResults,
      threshold,
    } : "skip"
  );

  // Notify parent component when results change
  React.useEffect(() => {
    if (onResultsChange && similarProducts) {
      onResultsChange(similarProducts);
    }
  }, [similarProducts, onResultsChange]);

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      setSearchImage(file);
      const preview = URL.createObjectURL(file);
      setSearchImagePreview(preview);
      
      // Auto-search when image is selected
      handleSearch(file);
    }
    
    // Reset the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSearch = async (file?: File) => {
    const imageFile = file || searchImage;
    if (!imageFile) return;

    setIsSearching(true);
    try {
      const imageUrl = URL.createObjectURL(imageFile);
      const embedding = await generateEmbedding({ imageUrl });
      setSearchEmbedding(embedding);
      
      // Clean up the temporary URL
      URL.revokeObjectURL(imageUrl);
    } catch (error) {
      console.error("Error generating embedding:", error);
    } finally {
      setIsSearching(false);
    }
  };

  const clearSearch = () => {
    setSearchImage(null);
    setSearchEmbedding(null);
    if (searchImagePreview) {
      URL.revokeObjectURL(searchImagePreview);
      setSearchImagePreview(null);
    }
    if (onResultsChange) {
      onResultsChange([]);
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center space-x-2">
            <Sparkles className="h-4 w-4 text-blue-500" />
            <span>Visual Search</span>
          </CardTitle>
          <CardDescription className="text-sm">
            Upload an image to find visually similar products
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Upload Area */}
          <div 
            className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center cursor-pointer hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            {searchImagePreview ? (
              <div className="relative inline-block">
                <Image
                  src={searchImagePreview}
                  alt="Search image"
                  width={120}
                  height={120}
                  className="rounded-lg object-cover"
                />
                <Button
                  size="sm"
                  variant="destructive"
                  className="absolute -top-2 -right-2 h-5 w-5 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    clearSearch();
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ) : (
              <>
                <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                <p className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                  Click to upload
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  PNG, JPG, WEBP
                </p>
              </>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            className="hidden"
            onChange={handleImageSelect}
          />

          {/* Search Status */}
          {isSearching && (
            <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Analyzing image...</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results Section */}
      {similarProducts && similarProducts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Similar Products</CardTitle>
            <CardDescription className="text-sm">
              {similarProducts.length} matches found
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              {similarProducts.slice(0, 4).map((product) => (
                <div key={product._id} className="space-y-2">
                  {/* Product Image */}
                  {product.images.length > 0 && (
                    <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
                      <Image
                        src={typeof product.images[0] === 'string' ? product.images[0] : ''}
                        alt={product.title}
                        width={120}
                        height={120}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}

                  {/* Product Info */}
                  <div className="space-y-1">
                    <h4 className="text-xs font-medium line-clamp-2">
                      {product.title}
                    </h4>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium">
                        ${product.finalPrice}
                      </span>
                      <Badge variant="outline" className="text-xs px-1 py-0">
                        {Math.round((product.similarity || 0) * 100)}%
                      </Badge>
                    </div>

                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full h-6 text-xs"
                      asChild
                    >
                      <Link href={`/dashboard/products/${product._id}`}>
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {similarProducts.length > 4 && (
              <div className="mt-3 text-center">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  +{similarProducts.length - 4} more results available
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* No Results */}
      {searchEmbedding && similarProducts && similarProducts.length === 0 && (
        <Card>
          <CardContent className="text-center py-6">
            <ImageIcon className="h-8 w-8 mx-auto text-gray-400 mb-2" />
            <p className="text-sm text-gray-500 dark:text-gray-400">
              No similar products found
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
