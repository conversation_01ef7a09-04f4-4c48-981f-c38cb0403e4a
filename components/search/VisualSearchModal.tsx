"use client";

import { useState, useRef } from "react";
import { useQuery, useAction } from "convex/react";
import { api } from "@/convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  Upload, 
  Image as ImageIcon, 
  X, 
  Search, 
  Loader2, 
  Eye,
  DollarSign,
  Package
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface VisualSearchModalProps {
  trigger?: React.ReactNode;
  onProductSelect?: (productId: string) => void;
}

export default function VisualSearchModal({ 
  trigger, 
  onProductSelect 
}: VisualSearchModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchImage, setSearchImage] = useState<File | null>(null);
  const [searchImagePreview, setSearchImagePreview] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);

  const [searchEmbedding, setSearchEmbedding] = useState<number[] | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const generateEmbedding = useAction(api.embeddings.generateImageEmbedding);
  
  // Query for similar products when we have an embedding
  const similarProducts = useQuery(
    api.products.searchProductsByImage,
    searchEmbedding ? {
      targetEmbedding: searchEmbedding,
      limit: 12,
      threshold: 0.6,
    } : "skip"
  );

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      setSearchImage(file);
      const preview = URL.createObjectURL(file);
      setSearchImagePreview(preview);
    }
    
    // Reset the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSearch = async () => {
    if (!searchImage) return;

    setIsSearching(true);
    try {
      const imageUrl = URL.createObjectURL(searchImage);
      const embedding = await generateEmbedding({ imageUrl });
      setSearchEmbedding(embedding);
      
      // Clean up the temporary URL
      URL.revokeObjectURL(imageUrl);
    } catch (error) {
      console.error("Error generating embedding:", error);
      alert("Failed to process image. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  const clearSearch = () => {
    setSearchImage(null);
    setSearchEmbedding(null);
    if (searchImagePreview) {
      URL.revokeObjectURL(searchImagePreview);
      setSearchImagePreview(null);
    }
  };

  const handleClose = () => {
    clearSearch();
    setIsOpen(false);
  };

  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="destructive">Out of Stock</Badge>;
    } else if (stock < 10) {
      return <Badge variant="destructive">Low Stock</Badge>;
    } else {
      return <Badge variant="default">In Stock</Badge>;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <ImageIcon className="h-4 w-4 mr-2" />
            Visual Search
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Visual Product Search</DialogTitle>
          <DialogDescription>
            Upload an image to find similar products using AI-powered visual search
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Image Upload Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Upload Search Image</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Upload Area */}
              <div 
                className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                {searchImagePreview ? (
                  <div className="relative inline-block">
                    <Image
                      src={searchImagePreview}
                      alt="Search image"
                      width={200}
                      height={200}
                      className="rounded-lg object-cover max-h-48"
                    />
                    <Button
                      size="sm"
                      variant="destructive"
                      className="absolute -top-2 -right-2 h-6 w-6 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        clearSearch();
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ) : (
                  <>
                    <Upload className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                    <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      Click to upload an image
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      PNG, JPG, WEBP up to 10MB
                    </p>
                  </>
                )}
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleImageSelect}
              />

              {/* Search Button */}
              {searchImage && !searchEmbedding && (
                <Button 
                  onClick={handleSearch} 
                  disabled={isSearching}
                  className="w-full"
                >
                  {isSearching ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing Image...
                    </>
                  ) : (
                    <>
                      <Search className="h-4 w-4 mr-2" />
                      Find Similar Products
                    </>
                  )}
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Search Results */}
          {similarProducts && similarProducts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Similar Products Found</CardTitle>
                <CardDescription>
                  {similarProducts.length} products found with visual similarity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {similarProducts.map((product) => (
                    <Card key={product._id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          {/* Product Image */}
                          {product.images.length > 0 && (
                            <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
                              <Image
                                src={typeof product.images[0] === 'string' ? product.images[0] : ''}
                                alt={product.title}
                                width={200}
                                height={200}
                                className="w-full h-full object-cover"
                              />
                            </div>
                          )}

                          {/* Product Info */}
                          <div className="space-y-2">
                            <h3 className="font-medium text-sm line-clamp-2">
                              {product.title}
                            </h3>
                            
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-1">
                                <DollarSign className="h-3 w-3 text-gray-500" />
                                <span className="text-sm font-medium">
                                  ${product.finalPrice}
                                </span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Package className="h-3 w-3 text-gray-500" />
                                <span className="text-xs text-gray-500">
                                  {product.stockCount}
                                </span>
                              </div>
                            </div>

                            {/* Similarity Score */}
                            <div className="flex items-center justify-between">
                              <Badge variant="outline" className="text-xs">
                                {Math.round((product.similarity || 0) * 100)}% match
                              </Badge>
                              {getStockBadge(product.stockCount)}
                            </div>

                            {/* Actions */}
                            <div className="flex space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                className="flex-1"
                                asChild
                              >
                                <Link href={`/dashboard/products/${product._id}`}>
                                  <Eye className="h-3 w-3 mr-1" />
                                  View
                                </Link>
                              </Button>
                              {onProductSelect && (
                                <Button
                                  size="sm"
                                  className="flex-1"
                                  onClick={() => {
                                    onProductSelect(product._id);
                                    handleClose();
                                  }}
                                >
                                  Select
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* No Results */}
          {searchEmbedding && similarProducts && similarProducts.length === 0 && (
            <Card>
              <CardContent className="text-center py-8">
                <ImageIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500 dark:text-gray-400 mb-2">
                  No similar products found
                </p>
                <p className="text-sm text-gray-400">
                  Try uploading a different image or adjust the search criteria
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
