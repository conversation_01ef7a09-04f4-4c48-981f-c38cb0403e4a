import { NextRequest } from "next/server";
import { Ratelimit } from "@upstash/ratelimit";
import { redis } from "@/lib/redis"; // Import our new Redis client

// Extended interface for Vercel Edge Runtime
interface VercelNextRequest extends NextRequest {
  geo?: {
    ip?: string;
    city?: string;
    country?: string;
    region?: string;
    latitude?: string;
    longitude?: string;
  };
  ip?: string;
}

export const rateLimiters = {
  // 5 attempts per 15 minutes for authentication actions
  auth: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(5, "15 m"),
    prefix: "ratelimit:auth",
    analytics: true,
  }),
  // 100 requests per minute for general API usage
  api_general: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(100, "1 m"),
    prefix: "ratelimit:api_general",
    analytics: true,
  }),
  // 20 requests per minute for sensitive or expensive API endpoints
  api_strict: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(20, "1 m"),
    prefix: "ratelimit:api_strict",
    analytics: true,
  }),
  // 50 actions per minute for admin panel operations
  admin_actions: new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(50, "1 m"),
    prefix: "ratelimit:admin_actions",
    analytics: true,
  }),
};

/**
 * Extracts the real IP address from various headers and sources
 * @param request The NextRequest object
 * @returns The IP address as a string
 */
function extractIPAddress(request: NextRequest): string {
  // Try to get IP from Vercel Edge Runtime
  const vercelRequest = request as VercelNextRequest;
  if (vercelRequest.ip) {
    return vercelRequest.ip;
  }
  
  if (vercelRequest.geo?.ip) {
    return vercelRequest.geo.ip;
  }

  // Extract from common proxy headers
  const forwarded = request.headers.get('x-forwarded-for');
  if (forwarded) {
    // x-forwarded-for can contain multiple IPs separated by commas
    // The first one is the original client IP
    return forwarded.split(',')[0].trim();
  }

  const realIP = request.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }

  // Fallback to connection info
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // If none found, return localhost
  return '127.0.0.1';
}

/**
 * Gets a unique identifier for the client making the request.
 * Prioritizes an authenticated user ID header, falling back to the IP address.
 * @param request The NextRequest object.
 * @returns A string identifier for rate limiting.
 */
export function getClientIdentifier(request: NextRequest): string {
  const userId = request.headers.get('x-user-id');
  if (userId) {
    return `user:${userId}`;
  }

  const ip = extractIPAddress(request);
  return `ip:${ip}`;
}

// ============================================================================
// SECURITY HEADERS
// ============================================================================

export const SECURITY_HEADERS = {
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
  // PRODUCTION-READY: Added 'preload' for better security
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  // PRODUCTION-READY: Updated CSP with a recommendation for production
  'Content-Security-Policy': [
    "default-src 'self'",
    // NOTE FOR PRODUCTION: 'unsafe-inline' and 'unsafe-eval' are insecure.
    // For a production build, you should use a nonce-based strategy for scripts and styles.
    // See Next.js docs for CSP: https://nextjs.org/docs/app/building-your-application/configuring/content-security-policy
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https:",
    "font-src 'self'",
    "connect-src 'self' https://*.convex.cloud wss://*.convex.cloud",
    "frame-ancestors 'none'",
    "form-action 'self'",
  ].join('; '),
};

// Input sanitization
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=\s*[^>\s]*/gi, '') // Remove event handlers
    .trim();
}

// XSS prevention
export function escapeHtml(input: string): string {
  const map: Record<string, string> = {
    '&': '&',
    '<': '<',
    '>': '>',
    '"': '"',
    "'": '&#39;',
    '/': '&#x2F;',
  };
  
  return input.replace(/[&<>"'/]/g, (s) => map[s]);
}

// Password strength validation
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  errors: string[];
  score: number; // 0-4
} {
  const errors: string[] = [];
  let score = 0;

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  } else {
    score++;
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  } else {
    score++;
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  } else {
    score++;
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  } else {
    score++;
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  } else {
    score++;
  }

  return {
    isValid: errors.length === 0,
    errors,
    score: Math.min(score, 4),
  };
}

/**
 * Generates a cryptographically secure random string using the Web Crypto API.
 * @param length The desired length of the token.
 * @returns A secure, random string.
 */
export function generateSecureToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const randomValues = new Uint32Array(length);

  if (typeof globalThis.crypto?.getRandomValues !== 'function') {
    throw new Error('Secure token generation requires the Web Crypto API, not available in this environment.');
  }

  globalThis.crypto.getRandomValues(randomValues);

  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars[randomValues[i] % chars.length];
  }
  
  return result;
}

// Audit logging interface
export interface AuditLogEntry {
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, unknown>;
  timestamp: number;
  ipAddress?: string;
  userAgent?: string;
}

// Create audit log entry
export function createAuditLog(
  userId: string,
  action: string,
  resource: string,
  options: {
    resourceId?: string;
    details?: Record<string, unknown>;
    request?: NextRequest;
  } = {}
): AuditLogEntry {
  return {
    userId,
    action,
    resource,
    resourceId: options.resourceId,
    details: options.details,
    timestamp: Date.now(),
    ipAddress: options.request ? getClientIdentifier(options.request) : undefined,
    userAgent: options.request?.headers.get('user-agent') || undefined,
  };
}

// Common audit actions
export const AUDIT_ACTIONS = {
  // Authentication
  LOGIN: 'login',
  LOGOUT: 'logout',
  LOGIN_FAILED: 'login_failed',
  
  // User management
  USER_CREATED: 'user_created',
  USER_UPDATED: 'user_updated',
  USER_DELETED: 'user_deleted',
  
  // Admin management
  ADMIN_CREATED: 'admin_created',
  ADMIN_UPDATED: 'admin_updated',
  ADMIN_DEACTIVATED: 'admin_deactivated',
  
  // Product management
  PRODUCT_CREATED: 'product_created',
  PRODUCT_UPDATED: 'product_updated',
  PRODUCT_DELETED: 'product_deleted',
  
  // Order management
  ORDER_CREATED: 'order_created',
  ORDER_UPDATED: 'order_updated',
  ORDER_STATUS_CHANGED: 'order_status_changed',
  
  // System
  SYSTEM_SETTINGS_CHANGED: 'system_settings_changed',
  BULK_OPERATION: 'bulk_operation',
} as const;

// Validate file upload
export function validateFileUpload(file: File, options: {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  allowedExtensions?: string[];
}): { isValid: boolean; error?: string } {
  const { maxSize = 5 * 1024 * 1024, allowedTypes = [], allowedExtensions = [] } = options;

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`,
    };
  }

  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not allowed`,
    };
  }

  if (allowedExtensions.length > 0) {
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !allowedExtensions.includes(extension)) {
      return {
        isValid: false,
        error: `File extension must be one of: ${allowedExtensions.join(', ')}`,
      };
    }
  }

  return { isValid: true };
}
