// Business logic for pricing calculations and validations

export interface PricingCalculation {
  priceInYuan: number;
  serviceFee: number;
  finalPrice: number;
  exchangeRate?: number;
  breakdown: {
    basePrice: number;
    serviceFeeAmount: number;
    exchangeFeeAmount?: number;
    totalBeforeTax: number;
    taxAmount?: number;
    finalAmount: number;
  };
}

export interface ServiceFeeConfig {
  percentage: number; // e.g., 0.15 for 15%
  minimumFee: number; // minimum service fee in final currency
  maximumFee?: number; // optional maximum service fee
}

// Default service fee configuration
export const DEFAULT_SERVICE_FEE_CONFIG: ServiceFeeConfig = {
  percentage: 0.15, // 15%
  minimumFee: 5, // $5 minimum
  maximumFee: 100, // $100 maximum
};

// Calculate service fee based on price and configuration
export function calculateServiceFee(
  priceInYuan: number,
  config: ServiceFeeConfig = DEFAULT_SERVICE_FEE_CONFIG,
  exchangeRate: number = 0.14 // Default CNY to USD rate
): number {
  const basePriceInUSD = priceInYuan * exchangeRate;
  const percentageFee = basePriceInUSD * config.percentage;
  
  let serviceFee = Math.max(percentageFee, config.minimumFee);
  
  if (config.maximumFee) {
    serviceFee = Math.min(serviceFee, config.maximumFee);
  }
  
  return Math.round(serviceFee * 100) / 100; // Round to 2 decimal places
}

// Calculate final price including all fees
export function calculateFinalPrice(
  priceInYuan: number,
  serviceFee?: number,
  exchangeRate: number = 0.14,
  config: ServiceFeeConfig = DEFAULT_SERVICE_FEE_CONFIG
): PricingCalculation {
  const basePrice = priceInYuan * exchangeRate;
  const calculatedServiceFee = serviceFee || calculateServiceFee(priceInYuan, config, exchangeRate);
  const finalPrice = basePrice + calculatedServiceFee;
  
  return {
    priceInYuan,
    serviceFee: calculatedServiceFee,
    finalPrice: Math.round(finalPrice * 100) / 100,
    exchangeRate,
    breakdown: {
      basePrice: Math.round(basePrice * 100) / 100,
      serviceFeeAmount: calculatedServiceFee,
      exchangeFeeAmount: 0, // Could be added later
      totalBeforeTax: Math.round((basePrice + calculatedServiceFee) * 100) / 100,
      taxAmount: 0, // Could be added later
      finalAmount: Math.round(finalPrice * 100) / 100,
    },
  };
}

// Validate pricing data
export function validatePricing(pricing: Partial<PricingCalculation>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!pricing.priceInYuan || pricing.priceInYuan <= 0) {
    errors.push("Price in Yuan must be positive");
  }
  
  if (!pricing.serviceFee || pricing.serviceFee < 0) {
    errors.push("Service fee must be non-negative");
  }
  
  if (!pricing.finalPrice || pricing.finalPrice <= 0) {
    errors.push("Final price must be positive");
  }
  
  // Check if final price calculation is consistent
  if (pricing.priceInYuan && pricing.serviceFee && pricing.finalPrice) {
    const expectedCalculation = calculateFinalPrice(pricing.priceInYuan, pricing.serviceFee);
    const tolerance = 0.01; // Allow 1 cent tolerance
    
    if (Math.abs(expectedCalculation.finalPrice - pricing.finalPrice) > tolerance) {
      errors.push("Final price calculation is inconsistent");
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Group buy pricing calculations
export interface GroupBuyTier {
  quantity: number;
  price: number;
}

export function calculateGroupBuyPricing(
  basePrice: number,
  tiers: GroupBuyTier[]
): GroupBuyTier[] {
  // Sort tiers by quantity
  const sortedTiers = [...tiers].sort((a, b) => a.quantity - b.quantity);
  
  // Validate that prices decrease as quantity increases
  for (let i = 1; i < sortedTiers.length; i++) {
    if (sortedTiers[i].price >= sortedTiers[i - 1].price) {
      throw new Error("Group buy prices must decrease as quantity increases");
    }
  }
  
  // Validate that all prices are less than base price
  for (const tier of sortedTiers) {
    if (tier.price >= basePrice) {
      throw new Error("Group buy prices must be less than base price");
    }
  }
  
  return sortedTiers;
}

// Calculate savings for group buy
export function calculateGroupBuySavings(
  basePrice: number,
  groupBuyPrice: number,
  quantity: number = 1
): {
  savingsPerItem: number;
  totalSavings: number;
  savingsPercentage: number;
} {
  const savingsPerItem = basePrice - groupBuyPrice;
  const totalSavings = savingsPerItem * quantity;
  const savingsPercentage = (savingsPerItem / basePrice) * 100;
  
  return {
    savingsPerItem: Math.round(savingsPerItem * 100) / 100,
    totalSavings: Math.round(totalSavings * 100) / 100,
    savingsPercentage: Math.round(savingsPercentage * 100) / 100,
  };
}

// Bulk pricing calculations for admin operations
export function calculateBulkPricing(
  items: Array<{ priceInYuan: number; quantity: number }>,
  exchangeRate: number = 0.14,
  config: ServiceFeeConfig = DEFAULT_SERVICE_FEE_CONFIG
): {
  totalPriceInYuan: number;
  totalServiceFee: number;
  totalFinalPrice: number;
  itemBreakdown: Array<PricingCalculation & { quantity: number }>;
} {
  let totalPriceInYuan = 0;
  let totalServiceFee = 0;
  let totalFinalPrice = 0;
  
  const itemBreakdown = items.map(item => {
    const pricing = calculateFinalPrice(item.priceInYuan, undefined, exchangeRate, config);
    const itemTotal = {
      ...pricing,
      quantity: item.quantity,
      finalPrice: pricing.finalPrice * item.quantity,
      serviceFee: pricing.serviceFee * item.quantity,
    };
    
    totalPriceInYuan += item.priceInYuan * item.quantity;
    totalServiceFee += itemTotal.serviceFee;
    totalFinalPrice += itemTotal.finalPrice;
    
    return itemTotal;
  });
  
  return {
    totalPriceInYuan: Math.round(totalPriceInYuan * 100) / 100,
    totalServiceFee: Math.round(totalServiceFee * 100) / 100,
    totalFinalPrice: Math.round(totalFinalPrice * 100) / 100,
    itemBreakdown,
  };
}

// Price comparison utilities
export function comparePrices(price1: number, price2: number): {
  difference: number;
  percentageDifference: number;
  isHigher: boolean;
  isLower: boolean;
  isEqual: boolean;
} {
  const difference = price1 - price2;
  const percentageDifference = price2 !== 0 ? (difference / price2) * 100 : 0;
  
  return {
    difference: Math.round(difference * 100) / 100,
    percentageDifference: Math.round(percentageDifference * 100) / 100,
    isHigher: difference > 0,
    isLower: difference < 0,
    isEqual: Math.abs(difference) < 0.01, // Within 1 cent
  };
}

// Format price for display
export function formatPrice(
  price: number,
  currency: string = "USD",
  locale: string = "en-US"
): string {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
  }).format(price);
}

// Format Yuan price for display
export function formatYuanPrice(price: number): string {
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: "CNY",
  }).format(price);
}
