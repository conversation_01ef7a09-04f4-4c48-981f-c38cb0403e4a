// Data transformation utilities for business logic

import type { Id } from "@/convex/_generated/dataModel";

// Product data transformations
export interface ProductSummary {
  id: Id<"products">;
  title: string;
  finalPrice: number;
  stockCount: number;
  status: string;
  images: string[];
  supplier?: {
    id: Id<"suppliers">;
    name: string;
  };
}

export function transformProductToSummary(product: any): ProductSummary {
  return {
    id: product._id,
    title: product.title,
    finalPrice: product.finalPrice,
    stockCount: product.stockCount,
    status: product.status,
    images: product.images || [],
    supplier: product.supplier ? {
      id: product.supplier.id,
      name: product.supplier.name,
    } : undefined,
  };
}

// Order data transformations
export interface OrderSummary {
  id: Id<"orders">;
  status: string;
  totalAmount: number;
  itemCount: number;
  customerName: string;
  customerEmail: string;
  createdAt: number;
  assignedTo?: {
    id: Id<"users">;
    name: string;
  };
}

export function transformOrderToSummary(order: any): OrderSummary {
  return {
    id: order._id,
    status: order.status,
    totalAmount: order.totalAmount,
    itemCount: order.items?.length || 0,
    customerName: order.user?.name || "Unknown",
    customerEmail: order.user?.email || "Unknown",
    createdAt: order._creationTime,
    assignedTo: order.assignedUser ? {
      id: order.assignedUser.id,
      name: order.assignedUser.name,
    } : undefined,
  };
}

// Analytics data transformations
export interface DashboardMetrics {
  orders: {
    total: number;
    new: number;
    actionRequired: number;
    shipped: number;
    delivered: number;
    revenue: number;
  };
  products: {
    total: number;
    active: number;
    lowStock: number;
    outOfStock: number;
  };
  suppliers: {
    total: number;
    active: number;
  };
  groupBuys: {
    active: number;
    completed: number;
    totalParticipants: number;
  };
}

export function transformStatsToDashboardMetrics(stats: {
  orderStats: any;
  productStats: any;
  supplierStats: any;
  groupBuyStats: any;
}): DashboardMetrics {
  return {
    orders: {
      total: stats.orderStats?.total || 0,
      new: stats.orderStats?.new || 0,
      actionRequired: stats.orderStats?.actionRequired || 0,
      shipped: stats.orderStats?.shipped || 0,
      delivered: stats.orderStats?.delivered || 0,
      revenue: stats.orderStats?.totalRevenue || 0,
    },
    products: {
      total: stats.productStats?.total || 0,
      active: stats.productStats?.active || 0,
      lowStock: stats.productStats?.lowStock || 0,
      outOfStock: stats.productStats?.outOfStock || 0,
    },
    suppliers: {
      total: stats.supplierStats?.total || 0,
      active: stats.supplierStats?.active || 0,
    },
    groupBuys: {
      active: stats.groupBuyStats?.active || 0,
      completed: stats.groupBuyStats?.completed || 0,
      totalParticipants: stats.groupBuyStats?.totalParticipants || 0,
    },
  };
}

// Table data transformations for admin interfaces
export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  filterable?: boolean;
  type?: "text" | "number" | "date" | "status" | "currency" | "image";
}

export interface TableData {
  columns: TableColumn[];
  rows: Record<string, any>[];
  totalCount: number;
  pageSize: number;
  currentPage: number;
}

export function transformProductsToTableData(
  products: any[],
  pagination?: { totalCount: number; pageSize: number; currentPage: number }
): TableData {
  const columns: TableColumn[] = [
    { key: "title", label: "Product", sortable: true, filterable: true, type: "text" },
    { key: "supplier", label: "Supplier", sortable: true, filterable: true, type: "text" },
    { key: "finalPrice", label: "Price", sortable: true, type: "currency" },
    { key: "stockCount", label: "Stock", sortable: true, type: "number" },
    { key: "status", label: "Status", sortable: true, filterable: true, type: "status" },
    { key: "images", label: "Images", type: "image" },
  ];

  const rows = products.map(product => ({
    id: product._id,
    title: product.title,
    supplier: product.supplier?.name || "Unknown",
    finalPrice: product.finalPrice,
    stockCount: product.stockCount,
    status: product.status,
    images: product.images?.[0] || null,
    _raw: product, // Keep raw data for actions
  }));

  return {
    columns,
    rows,
    totalCount: pagination?.totalCount || products.length,
    pageSize: pagination?.pageSize || 20,
    currentPage: pagination?.currentPage || 1,
  };
}

export function transformOrdersToTableData(
  orders: any[],
  pagination?: { totalCount: number; pageSize: number; currentPage: number }
): TableData {
  const columns: TableColumn[] = [
    { key: "id", label: "Order ID", sortable: true, type: "text" },
    { key: "customer", label: "Customer", sortable: true, filterable: true, type: "text" },
    { key: "status", label: "Status", sortable: true, filterable: true, type: "status" },
    { key: "totalAmount", label: "Total", sortable: true, type: "currency" },
    { key: "itemCount", label: "Items", sortable: true, type: "number" },
    { key: "createdAt", label: "Created", sortable: true, type: "date" },
    { key: "assignedTo", label: "Assigned To", filterable: true, type: "text" },
  ];

  const rows = orders.map(order => ({
    id: order._id,
    customer: order.user?.name || "Unknown",
    status: order.status,
    totalAmount: order.totalAmount,
    itemCount: order.items?.length || 0,
    createdAt: order._creationTime,
    assignedTo: order.assignedUser?.name || "Unassigned",
    _raw: order,
  }));

  return {
    columns,
    rows,
    totalCount: pagination?.totalCount || orders.length,
    pageSize: pagination?.pageSize || 20,
    currentPage: pagination?.currentPage || 1,
  };
}

// Search and filter utilities
export interface SearchFilters {
  query?: string;
  status?: string[];
  dateRange?: {
    start: number;
    end: number;
  };
  priceRange?: {
    min: number;
    max: number;
  };
  tags?: string[];
  supplierId?: Id<"suppliers">;
  assignedTo?: Id<"users">;
}

export function applySearchFilters<T extends Record<string, any>>(
  items: T[],
  filters: SearchFilters,
  searchFields: string[] = ["title", "description"]
): T[] {
  let filtered = [...items];

  // Text search
  if (filters.query) {
    const query = filters.query.toLowerCase();
    filtered = filtered.filter(item =>
      searchFields.some(field => {
        const value = item[field];
        return value && value.toString().toLowerCase().includes(query);
      })
    );
  }

  // Status filter
  if (filters.status && filters.status.length > 0) {
    filtered = filtered.filter(item => filters.status!.includes(item.status));
  }

  // Date range filter
  if (filters.dateRange) {
    filtered = filtered.filter(item => {
      const itemDate = item._creationTime || item.createdAt || 0;
      return itemDate >= filters.dateRange!.start && itemDate <= filters.dateRange!.end;
    });
  }

  // Price range filter
  if (filters.priceRange) {
    filtered = filtered.filter(item => {
      const price = item.finalPrice || item.totalAmount || 0;
      return price >= filters.priceRange!.min && price <= filters.priceRange!.max;
    });
  }

  // Tags filter
  if (filters.tags && filters.tags.length > 0) {
    filtered = filtered.filter(item =>
      item.tags && filters.tags!.some(tag => item.tags.includes(tag))
    );
  }

  // Supplier filter
  if (filters.supplierId) {
    filtered = filtered.filter(item => item.supplierId === filters.supplierId);
  }

  // Assigned to filter
  if (filters.assignedTo) {
    filtered = filtered.filter(item => item.assignedTo === filters.assignedTo);
  }

  return filtered;
}

// Sorting utilities
export type SortDirection = "asc" | "desc";

export function applySorting<T extends Record<string, any>>(
  items: T[],
  sortKey: string,
  direction: SortDirection = "asc"
): T[] {
  return [...items].sort((a, b) => {
    const aValue = a[sortKey];
    const bValue = b[sortKey];

    // Handle null/undefined values
    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return direction === "asc" ? 1 : -1;
    if (bValue == null) return direction === "asc" ? -1 : 1;

    // Handle different data types
    if (typeof aValue === "string" && typeof bValue === "string") {
      const comparison = aValue.localeCompare(bValue);
      return direction === "asc" ? comparison : -comparison;
    }

    if (typeof aValue === "number" && typeof bValue === "number") {
      const comparison = aValue - bValue;
      return direction === "asc" ? comparison : -comparison;
    }

    // Default comparison
    const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    return direction === "asc" ? comparison : -comparison;
  });
}

// Pagination utilities
export interface PaginationResult<T> {
  items: T[];
  totalCount: number;
  pageSize: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export function applyPagination<T>(
  items: T[],
  page: number = 1,
  pageSize: number = 20
): PaginationResult<T> {
  const totalCount = items.length;
  const totalPages = Math.ceil(totalCount / pageSize);
  const currentPage = Math.max(1, Math.min(page, totalPages));
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedItems = items.slice(startIndex, endIndex);

  return {
    items: paginatedItems,
    totalCount,
    pageSize,
    currentPage,
    totalPages,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
  };
}

// Export utilities
export function transformDataForExport(
  data: any[],
  columns: { key: string; label: string }[]
): Array<Record<string, any>> {
  return data.map(item => {
    const exportItem: Record<string, any> = {};
    columns.forEach(column => {
      exportItem[column.label] = item[column.key] || "";
    });
    return exportItem;
  });
}

// CSV export utility
export function convertToCSV(data: Array<Record<string, any>>): string {
  if (data.length === 0) return "";

  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(","),
    ...data.map(row =>
      headers.map(header => {
        const value = row[header];
        // Escape commas and quotes in CSV
        if (typeof value === "string" && (value.includes(",") || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(",")
    )
  ].join("\n");

  return csvContent;
}
