// Business logic for order status transitions and validations

export type OrderStatus = 
  | "new"
  | "sourcing"
  | "action_required"
  | "shipped"
  | "delivered"
  | "cancelled";

export interface StatusTransition {
  from: OrderStatus;
  to: OrderStatus;
  isValid: boolean;
  requiresPermission?: string;
  requiresData?: string[];
  description: string;
}

// Define valid status transitions
export const STATUS_TRANSITIONS: StatusTransition[] = [
  // From new
  {
    from: "new",
    to: "sourcing",
    isValid: true,
    description: "Begin sourcing the order items",
  },
  {
    from: "new",
    to: "cancelled",
    isValid: true,
    description: "Cancel the order before processing",
  },
  
  // From sourcing
  {
    from: "sourcing",
    to: "action_required",
    isValid: true,
    requiresData: ["issueDescription"],
    description: "Issue encountered during sourcing that requires customer action",
  },
  {
    from: "sourcing",
    to: "shipped",
    isValid: true,
    requiresData: ["trackingNumber"],
    description: "Items sourced and shipped to customer",
  },
  {
    from: "sourcing",
    to: "cancelled",
    isValid: true,
    description: "Cancel order during sourcing process",
  },
  
  // From action_required
  {
    from: "action_required",
    to: "sourcing",
    isValid: true,
    description: "Resume sourcing after customer action",
  },
  {
    from: "action_required",
    to: "shipped",
    isValid: true,
    requiresData: ["trackingNumber"],
    description: "Ship order after resolving issues",
  },
  {
    from: "action_required",
    to: "cancelled",
    isValid: true,
    description: "Cancel order due to unresolved issues",
  },
  
  // From shipped
  {
    from: "shipped",
    to: "delivered",
    isValid: true,
    description: "Order successfully delivered to customer",
  },
  {
    from: "shipped",
    to: "action_required",
    isValid: true,
    requiresData: ["issueDescription"],
    description: "Issue with shipment requiring attention",
  },
  
  // From delivered - final state, no transitions allowed

  // From cancelled - final state, no transitions allowed

  // Note: shipped and delivered orders cannot be cancelled
];

// Check if a status transition is valid
export function isValidStatusTransition(from: OrderStatus, to: OrderStatus): boolean {
  return STATUS_TRANSITIONS.some(
    transition => transition.from === from && transition.to === to && transition.isValid
  );
}

// Get valid next statuses for a given current status
export function getValidNextStatuses(currentStatus: OrderStatus): OrderStatus[] {
  return STATUS_TRANSITIONS
    .filter(transition => transition.from === currentStatus && transition.isValid)
    .map(transition => transition.to);
}

// Get transition details
export function getTransitionDetails(from: OrderStatus, to: OrderStatus): StatusTransition | null {
  return STATUS_TRANSITIONS.find(
    transition => transition.from === from && transition.to === to
  ) || null;
}

// Validate status transition with required data
export function validateStatusTransition(
  from: OrderStatus,
  to: OrderStatus,
  data?: Record<string, any>
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check if transition is allowed
  const transition = getTransitionDetails(from, to);
  if (!transition) {
    errors.push(`Invalid status transition from ${from} to ${to}`);
    return { isValid: false, errors, warnings };
  }
  
  // Check required data
  if (transition.requiresData) {
    for (const requiredField of transition.requiresData) {
      if (!data || !data[requiredField]) {
        errors.push(`${requiredField} is required for this status transition`);
      }
    }
  }
  
  // Specific validations
  if (to === "shipped") {
    if (!data?.trackingNumber || !data.trackingNumber.trim()) {
      errors.push("Tracking number is required when marking order as shipped");
    }
  }
  
  if (to === "action_required") {
    if (!data?.issueDescription || !data.issueDescription.trim()) {
      errors.push("Issue description is required when marking order as action required");
    }
  }
  
  if (to === "cancelled") {
    // Check if transition is valid first
    if (!transition.isValid) {
      // This will be caught by the earlier check, but add specific message for shipped/delivered
      if (from === "shipped" || from === "delivered") {
        errors.push("Cannot cancel order that has already been shipped or delivered");
      }
    }

    if (!data?.cancellationReason) {
      warnings.push("Consider providing a cancellation reason for better record keeping");
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// Get status display information
export function getStatusDisplayInfo(status: OrderStatus): {
  label: string;
  color: string;
  icon: string;
  description: string;
  isActive: boolean;
  isFinal: boolean;
} {
  const statusInfo = {
    new: {
      label: "New",
      color: "blue",
      icon: "plus-circle",
      description: "Order has been placed and is awaiting processing",
      isActive: true,
      isFinal: false,
    },
    sourcing: {
      label: "Sourcing",
      color: "yellow",
      icon: "search",
      description: "Currently sourcing items from suppliers",
      isActive: true,
      isFinal: false,
    },
    action_required: {
      label: "Action Required",
      color: "orange",
      icon: "exclamation-triangle",
      description: "Customer action or decision required to proceed",
      isActive: true,
      isFinal: false,
    },
    shipped: {
      label: "Shipped",
      color: "purple",
      icon: "truck",
      description: "Order has been shipped and is in transit",
      isActive: true,
      isFinal: false,
    },
    delivered: {
      label: "Delivered",
      color: "green",
      icon: "check-circle",
      description: "Order has been successfully delivered",
      isActive: false,
      isFinal: true,
    },
    cancelled: {
      label: "Cancelled",
      color: "red",
      icon: "x-circle",
      description: "Order has been cancelled",
      isActive: false,
      isFinal: true,
    },
  };
  
  return statusInfo[status];
}

// Calculate order processing time
export function calculateProcessingTime(
  createdAt: number,
  currentStatus: OrderStatus,
  statusHistory?: Array<{ status: OrderStatus; timestamp: number }>
): {
  totalProcessingTime: number; // in milliseconds
  timeInCurrentStatus: number;
  averageTimePerStatus: number;
  estimatedCompletionTime?: number;
} {
  const now = Date.now();
  const totalProcessingTime = now - createdAt;
  
  let timeInCurrentStatus = totalProcessingTime;
  let averageTimePerStatus = totalProcessingTime;
  
  if (statusHistory && statusHistory.length > 0) {
    // Find when current status started
    const currentStatusEntry = statusHistory
      .reverse()
      .find(entry => entry.status === currentStatus);
    
    if (currentStatusEntry) {
      timeInCurrentStatus = now - currentStatusEntry.timestamp;
    }
    
    // Calculate average time per status
    if (statusHistory.length > 1) {
      averageTimePerStatus = totalProcessingTime / statusHistory.length;
    }
  }
  
  // Estimate completion time based on current status
  let estimatedCompletionTime: number | undefined;
  const statusOrder: OrderStatus[] = ["new", "sourcing", "action_required", "shipped", "delivered"];
  const currentIndex = statusOrder.indexOf(currentStatus);
  
  if (currentIndex >= 0 && currentIndex < statusOrder.length - 1) {
    const remainingStatuses = statusOrder.length - 1 - currentIndex;
    estimatedCompletionTime = now + (averageTimePerStatus * remainingStatuses);
  }
  
  return {
    totalProcessingTime,
    timeInCurrentStatus,
    averageTimePerStatus,
    estimatedCompletionTime,
  };
}

// Format processing time for display
export function formatProcessingTime(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days} day${days > 1 ? 's' : ''}`;
  } else if (hours > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''}`;
  } else if (minutes > 0) {
    return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  } else {
    return `${seconds} second${seconds > 1 ? 's' : ''}`;
  }
}

// Get orders that need attention based on status and time
export function getOrdersNeedingAttention(
  orders: Array<{
    id: string;
    status: OrderStatus;
    createdAt: number;
    lastUpdated?: number;
  }>
): Array<{
  id: string;
  reason: string;
  priority: "high" | "medium" | "low";
  daysInStatus: number;
}> {
  const now = Date.now();
  const oneDayMs = 24 * 60 * 60 * 1000;
  const needingAttention: Array<{
    id: string;
    reason: string;
    priority: "high" | "medium" | "low";
    daysInStatus: number;
  }> = [];
  
  for (const order of orders) {
    const timeInStatus = now - (order.lastUpdated || order.createdAt);
    const daysInStatus = Math.floor(timeInStatus / oneDayMs);
    
    // Define attention thresholds for each status
    const thresholds = {
      new: { high: 1, medium: 0.5 }, // 1 day high, 12 hours medium
      sourcing: { high: 7, medium: 3 }, // 7 days high, 3 days medium
      action_required: { high: 3, medium: 1 }, // 3 days high, 1 day medium
      shipped: { high: 14, medium: 7 }, // 14 days high, 7 days medium
      delivered: { high: Infinity, medium: Infinity }, // No attention needed
      cancelled: { high: Infinity, medium: Infinity }, // No attention needed
    };
    
    const threshold = thresholds[order.status];
    let priority: "high" | "medium" | "low" | null = null;
    let reason = "";
    
    if (daysInStatus >= threshold.high) {
      priority = "high";
      reason = `Order has been in ${order.status} status for ${daysInStatus} days`;
    } else if (daysInStatus >= threshold.medium) {
      priority = "medium";
      reason = `Order has been in ${order.status} status for ${daysInStatus} days`;
    }
    
    if (priority) {
      needingAttention.push({
        id: order.id,
        reason,
        priority,
        daysInStatus,
      });
    }
  }
  
  // Sort by priority and days in status
  return needingAttention.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
    if (priorityDiff !== 0) return priorityDiff;
    return b.daysInStatus - a.daysInStatus;
  });
}
