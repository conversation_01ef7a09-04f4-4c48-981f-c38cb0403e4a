import { Redis } from '@upstash/redis';
import { env, hasRedisConfig, isTest } from './env';

// Create Redis client with proper error handling
export const redis = (() => {
  // In test environment, we can skip Redis initialization
  if (isTest) {
    return {} as Redis; // Mock Redis for tests
  }

  if (!hasRedisConfig) {
    throw new Error(
      'Missing Upstash Redis environment variables.\n' +
      'Please set UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN in your .env.local file.\n' +
      'Get these from https://console.upstash.com/'
    );
  }

  return new Redis({
    url: env.UPSTASH_REDIS_REST_URL,
    token: env.UPSTASH_REDIS_REST_TOKEN,
  });
})();