// lib/auth/permissions.tsx

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { PERMISSIONS, type Permission, type Role } from "@/convex/auth/permissions";

// Hook to get current admin user
export function useCurrentAdminUser() {
  return useQuery(api.auth.rbac.getCurrentAdminUser); 
}

export function useHasPermissions(permissions: Permission[]) {
  const adminUser = useCurrentAdminUser();
  const userPermissions = adminUser?.permissions || [];

  if (adminUser === undefined) {
    return {
      loading: true,
      hasAll: false,
      hasAny: false,
      permissions: {} as Record<Permission, boolean>
    };
  }

  const hasAll = permissions.every(p => userPermissions.includes(p));
  const hasAny = permissions.some(p => userPermissions.includes(p));

  const permissionMap = permissions.reduce((acc, p) => {
    acc[p] = userPermissions.includes(p);
    return acc;
  }, {} as Record<Permission, boolean>);

  return {
    loading: false,
    hasAll,
    hasAny,
    permissions: permissionMap
  };
}

// Hook to check if current user has a specific permission (wrapper around the new hook)
export function useHasPermission(permission: Permission) {
  const { loading, hasAll } = useHasPermissions([permission]);
  return { loading, hasPermission: hasAll };
}


// Component wrapper for permission-based rendering
// This component now works correctly with the fixed `useHasPermissions` hook.
interface PermissionGuardProps {
  permission: Permission | Permission[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAll?: boolean; // For multiple permissions, require all (default) or any
}

export function PermissionGuard({ 
  permission, 
  children, 
  fallback = null,
  requireAll = true 
}: PermissionGuardProps) {
  const permissions = Array.isArray(permission) ? permission : [permission];
  const { loading, hasAll, hasAny } = useHasPermissions(permissions);
  
  if (loading) {
    return <div className="animate-pulse bg-gray-200 h-4 w-16 rounded"></div>;
  }
  
  const hasPermission = requireAll ? hasAll : hasAny;
  
  return hasPermission ? <>{children}</> : <>{fallback}</>;
}

// Higher-order component for page-level permission checking
// This component also works correctly now.
export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  requiredPermission: Permission | Permission[],
  requireAll: boolean = true
) {
  return function PermissionWrappedComponent(props: P) {
    const permissions = Array.isArray(requiredPermission) ? requiredPermission : [requiredPermission];
    const { loading, hasAll, hasAny } = useHasPermissions(permissions);
    
    if (loading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      );
    }
    
    const hasPermission = requireAll ? hasAll : hasAny;
    
    if (!hasPermission) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600">You don&apos;t have permission to access this page.</p>
          </div>
        </div>
      );
    }
    
    return <Component {...props} />;
  };
}

// Role-based component wrapper
interface RoleGuardProps {
  role: Role | Role[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

// FIX 2: Simplified `RoleGuard`.
// The `requireAll` prop is logically problematic if a user can only have one role.
// This version simply checks if the user's role is one of the allowed roles.
export function RoleGuard({ 
  role, 
  children, 
  fallback = null,
}: RoleGuardProps) {
  const adminUser = useCurrentAdminUser();
  
  if (adminUser === undefined) {
    return <div className="animate-pulse bg-gray-200 h-4 w-16 rounded"></div>; // Loading state
  }
  
  if (!adminUser) {
    return <>{fallback}</>; // No user found
  }
  
  const allowedRoles = Array.isArray(role) ? role : [role];
  const hasRole = allowedRoles.includes(adminUser.role);
  
  return hasRole ? <>{children}</> : <>{fallback}</>;
}

// Utility function to check if user is admin (client-side)
export function useIsAdmin() {
  const adminUser = useCurrentAdminUser();
  return {
    isAdmin: !!adminUser,
    role: adminUser?.role,
    permissions: adminUser?.permissions || [],
    loading: adminUser === undefined
  };
}

// Export permissions for easy access
export { PERMISSIONS };
export type { Permission, Role };