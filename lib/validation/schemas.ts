import { v } from "convex/values";

// User validation schemas
export const userValidation = {
  email: v.string(),
  name: v.string(),
  phone: v.optional(v.string()),
};

// Product validation schemas
export const productValidation = {
  title: v.string(),
  description: v.string(),
  curationNotes: v.string(),
  supplierId: v.id("suppliers"),
  priceInYuan: v.number(),
  serviceFee: v.number(),
  finalPrice: v.number(),
  tags: v.array(v.string()),
  images: v.array(v.string()),
  stockCount: v.number(),
  status: v.union(v.literal("active"), v.literal("inactive"), v.literal("archived")),
};

// Order validation schemas
export const orderValidation = {
  userId: v.id("users"),
  items: v.array(v.object({
    productId: v.id("products"),
    quantity: v.number(),
    priceAtTime: v.number(),
    title: v.string(),
  })),
  status: v.union(
    v.literal("new"),
    v.literal("sourcing"),
    v.literal("action_required"),
    v.literal("shipped"),
    v.literal("delivered"),
    v.literal("cancelled")
  ),
  trackingNumber: v.optional(v.string()),
  shippingAddress: v.object({
    name: v.string(),
    address: v.string(),
    city: v.string(),
    country: v.string(),
    postalCode: v.string(),
  }),
  totalAmount: v.number(),
  assignedTo: v.optional(v.id("users")),
};

// Supplier validation schemas
export const supplierValidation = {
  name: v.string(),
  contactInfo: v.object({
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    address: v.optional(v.string()),
  }),
  platformUrl: v.optional(v.string()),
  rating: v.optional(v.number()),
  notes: v.optional(v.string()),
  isActive: v.boolean(),
};

// Group buy validation schemas
export const groupBuyValidation = {
  productId: v.id("products"),
  targetTiers: v.array(v.object({
    quantity: v.number(),
    price: v.number(),
  })),
  currentParticipants: v.number(),
  status: v.union(v.literal("active"), v.literal("completed"), v.literal("expired")),
  startTime: v.number(),
  endTime: v.number(),
};

// Input sanitization functions
export const sanitize = {
  // Remove HTML tags and dangerous characters
  html: (input: string): string => {
    return input
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/[<>'"&]/g, '') // Remove dangerous characters
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .trim();
  },
  
  // Sanitize email
  email: (input: string): string => {
    return input.toLowerCase().trim();
  },
  
  // Sanitize phone number
  phone: (input: string): string => {
    return input.replace(/[^\d+\-\s()]/g, '').replace(/\./g, ' ').replace(/\s+/g, ' ').trim();
  },
  
  // Sanitize numeric input
  number: (input: string | number): number => {
    const num = typeof input === 'string' ? parseFloat(input) : input;
    return isNaN(num) ? 0 : num;
  },
  
  // Sanitize price (ensure positive, max 2 decimal places)
  price: (input: string | number): number => {
    const num = sanitize.number(input);
    return Math.max(0, Math.round(num * 100) / 100);
  },
  
  // Sanitize URL
  url: (input: string): string => {
    try {
      const url = new URL(input);
      return url.toString();
    } catch {
      return '';
    }
  },
};

// Validation functions
export const validate = {
  // Email validation
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
  
  // Phone validation (basic international format)
  phone: (phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{6,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  },
  
  // URL validation
  url: (url: string): boolean => {
    try {
      const urlObj = new URL(url);
      // Reject dangerous protocols
      const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:'];
      if (dangerousProtocols.some(protocol => urlObj.protocol === protocol)) {
        return false;
      }
      return true;
    } catch {
      return false;
    }
  },
  
  // Price validation (positive number with max 2 decimal places)
  price: (price: number): boolean => {
    if (price < 0 || !Number.isFinite(price)) return false;
    const decimalPart = price.toString().split('.')[1];
    return !decimalPart || decimalPart.length <= 2;
  },
  
  // Stock count validation
  stockCount: (count: number): boolean => {
    return Number.isInteger(count) && count >= 0;
  },
  
  // Required string validation
  requiredString: (str: string, minLength: number = 1): boolean => {
    return typeof str === 'string' && str.trim().length >= minLength;
  },
  
  // Array validation
  nonEmptyArray: (arr: unknown[]): boolean => {
    return Array.isArray(arr) && arr.length > 0;
  },
};

// Error messages
export const errorMessages = {
  required: (field: string) => `${field} is required`,
  invalid: (field: string) => `${field} is invalid`,
  tooShort: (field: string, min: number) => `${field} must be at least ${min} characters`,
  tooLong: (field: string, max: number) => `${field} must be no more than ${max} characters`,
  invalidEmail: 'Please enter a valid email address',
  invalidPhone: 'Please enter a valid phone number',
  invalidUrl: 'Please enter a valid URL',
  invalidPrice: 'Price must be a positive number with at most 2 decimal places',
  invalidStock: 'Stock count must be a non-negative integer',
  unauthorized: 'You are not authorized to perform this action',
  notFound: (resource: string) => `${resource} not found`,
  alreadyExists: (resource: string) => `${resource} already exists`,
};

// Validation result type
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Generic validation function
export function validateFields(
  data: Record<string, unknown>,
  rules: Record<string, (value: unknown) => boolean | string>
): ValidationResult {
  const errors: string[] = [];
  
  for (const [field, rule] of Object.entries(rules)) {
    const value = data[field];
    const result = rule(value);
    
    if (result === false) {
      errors.push(errorMessages.invalid(field));
    } else if (typeof result === 'string') {
      errors.push(result);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}
