import { useMutation, useAction } from "convex/react";
import { api } from "@/convex/_generated/api";
import type { Id } from "@/convex/_generated/dataModel";

// Image upload hook
export function useImageUpload() {
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);
  const uploadProductImage = useMutation(api.files.uploadProductImage);

  const uploadImage = async (
    file: File,
    options?: {
      productId?: Id<"products">;
      altText?: string;
      onProgress?: (progress: number) => void;
    }
  ): Promise<{
    success: boolean;
    storageId?: Id<"_storage">;
    url?: string;
    error?: string;
  }> => {
    try {
      // Validate file before upload using client-side validation
      const validation = validateImageFile(file);

      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors[0] || "Invalid file",
        };
      }

      // Generate upload URL
      const uploadUrl = await generateUploadUrl();

      // Upload file to Convex storage
      const result = await fetch(uploadUrl, {
        method: "POST",
        headers: { "Content-Type": file.type },
        body: file,
      });

      if (!result.ok) {
        throw new Error(`Upload failed: ${result.statusText}`);
      }

      const { storageId } = await result.json();

      // Process the uploaded image
      const imageData = await uploadProductImage({
        storageId,
        productId: options?.productId,
        altText: options?.altText,
      });

      return {
        success: true,
        storageId: imageData.storageId,
        url: imageData.url,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Upload failed",
      };
    }
  };

  return { uploadImage };
}

// Multiple image upload hook
export function useBatchImageUpload() {
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);
  const batchUploadImages = useAction(api.files.batchUploadImages);

  const uploadImages = async (
    files: File[],
    options?: {
      productId?: Id<"products">;
      onProgress?: (progress: number) => void;
      onFileProgress?: (fileIndex: number, progress: number) => void;
    }
  ): Promise<{
    success: boolean;
    results: Array<{
      success: boolean;
      storageId?: Id<"_storage">;
      url?: string;
      error?: string;
      filename: string;
    }>;
    error?: string;
  }> => {
    try {
      const results = [];
      const uploadedFiles = [];

      // Upload each file to storage
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        try {
          options?.onFileProgress?.(i, 0);
          
          const uploadUrl = await generateUploadUrl();
          
          const result = await fetch(uploadUrl, {
            method: "POST",
            headers: { "Content-Type": file.type },
            body: file,
          });

          if (!result.ok) {
            throw new Error(`Upload failed: ${result.statusText}`);
          }

          const { storageId } = await result.json();
          uploadedFiles.push({
            storageId,
            altText: file.name,
          });

          options?.onFileProgress?.(i, 100);
          options?.onProgress?.((i + 1) / files.length * 50); // 50% for uploads
        } catch (error) {
          results.push({
            success: false,
            error: error instanceof Error ? error.message : "Upload failed",
            filename: file.name,
          });
        }
      }

      // Process uploaded files
      if (uploadedFiles.length > 0) {
        const processResults = await batchUploadImages({
          files: uploadedFiles,
          productId: options?.productId,
        });

        // Combine results
        processResults.forEach((result: any, index: number) => {
          if (result.success) {
            results.push({
              success: true,
              storageId: result.storageId,
              url: result.url,
              filename: files[index]?.name || "unknown",
            });
          } else {
            results.push({
              success: false,
              error: result.error,
              filename: files[index]?.name || "unknown",
            });
          }
        });

        options?.onProgress?.(100);
      }

      return {
        success: results.some(r => r.success),
        results,
      };
    } catch (error) {
      return {
        success: false,
        results: files.map(file => ({
          success: false,
          error: error instanceof Error ? error.message : "Upload failed",
          filename: file.name,
        })),
        error: error instanceof Error ? error.message : "Batch upload failed",
      };
    }
  };

  return { uploadImages };
}

// Image management hooks
export function useImageManagement() {
  const removeProductImage = useMutation(api.files.removeProductImage);
  const reorderProductImages = useMutation(api.files.reorderProductImages);
  const deleteFile = useMutation(api.files.deleteFile);

  const removeImage = async (productId: Id<"products">, imageUrl: string) => {
    try {
      await removeProductImage({ productId, imageUrl });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to remove image",
      };
    }
  };

  const reorderImages = async (productId: Id<"products">, imageUrls: string[]) => {
    try {
      await reorderProductImages({ productId, imageUrls });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to reorder images",
      };
    }
  };

  const deleteStorageFile = async (storageId: Id<"_storage">) => {
    try {
      await deleteFile({ storageId });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to delete file",
      };
    }
  };

  return {
    removeImage,
    reorderImages,
    deleteStorageFile,
  };
}

// Image validation utilities
export function validateImageFile(file: File): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check file type
  const allowedTypes = ["image/jpeg", "image/png", "image/webp", "image/gif"];
  if (!allowedTypes.includes(file.type)) {
    errors.push("File type not supported. Please use JPEG, PNG, WebP, or GIF.");
  }

  // Check file size (5MB limit)
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    errors.push("File size too large. Maximum size is 5MB.");
  }

  // Check filename
  if (file.name.length > 255) {
    errors.push("Filename too long. Maximum length is 255 characters.");
  }

  // Warnings
  if (file.size > 1024 * 1024) {
    warnings.push("Large file size. Consider optimizing the image for web use.");
  }

  if (file.type === "image/gif" && file.size > 2 * 1024 * 1024) {
    warnings.push("Large GIF file. Consider converting to video format for better performance.");
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// Image preview utilities
export function createImagePreview(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error("Failed to read file"));
      }
    };
    reader.onerror = () => reject(new Error("Failed to read file"));
    reader.readAsDataURL(file);
  });
}

// Image compression utilities (client-side)
export function compressImage(
  file: File,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
  } = {}
): Promise<File> {
  return new Promise((resolve, reject) => {
    const { maxWidth = 1200, maxHeight = 1200, quality = 0.8 } = options;

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            reject(new Error("Failed to compress image"));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error("Failed to load image"));
    img.src = URL.createObjectURL(file);
  });
}

// Drag and drop utilities
export function createDropZoneHandlers(
  onFiles: (files: File[]) => void,
  options: {
    accept?: string[];
    maxFiles?: number;
  } = {}
) {
  const { accept = ["image/*"], maxFiles = 10 } = options;

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    const validFiles = files.filter(file => {
      return accept.some(acceptType => {
        if (acceptType === "image/*") {
          return file.type.startsWith("image/");
        }
        return file.type === acceptType;
      });
    });

    const limitedFiles = validFiles.slice(0, maxFiles);
    onFiles(limitedFiles);
  };

  return {
    handleDragOver,
    handleDragEnter,
    handleDragLeave,
    handleDrop,
  };
}
