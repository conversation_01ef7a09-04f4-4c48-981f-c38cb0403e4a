"use node";

/**
 * Google Vertex AI Image Embeddings Integration
 *
 * This module provides utilities for generating image embeddings using
 * Google's Vertex AI Multimodal Embeddings API.
 */

import { PredictionServiceClient } from "@google-cloud/aiplatform";

// Initialize the Google AI Platform client
let client: PredictionServiceClient | null = null;

function getClient(): PredictionServiceClient {
  if (!client) {
    client = new PredictionServiceClient({
      credentials: {
        client_email: process.env.GOOGLE_CLOUD_CLIENT_EMAIL,
        private_key: process.env.GOOGLE_CLOUD_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
    });
  }
  return client;
}

/**
 * Configuration for the embedding service
 */
const EMBEDDING_CONFIG = {
  projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
  location: process.env.GOOGLE_CLOUD_LOCATION || "us-central1",
  model: "multimodalembedding@001",
};

/**
 * Generate embeddings for an image
 * 
 * @param imageUrl - URL of the image to process
 * @returns Promise<number[]> - Array of embedding values
 */
export async function generateImageEmbedding(imageUrl: string): Promise<number[]> {
  if (!EMBEDDING_CONFIG.projectId) {
    throw new Error("GCP_PROJECT_ID environment variable is required");
  }

  try {
    // Fetch the image data
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }

    const imageBuffer = await response.arrayBuffer();
    const encodedImage = Buffer.from(imageBuffer).toString("base64");

    // Prepare the request for Vertex AI
    const endpoint = `projects/${EMBEDDING_CONFIG.projectId}/locations/${EMBEDDING_CONFIG.location}/publishers/google/models/${EMBEDDING_CONFIG.model}`;

    const instance = {
      structValue: {
        fields: {
          image: {
            structValue: {
              fields: {
                bytesBase64Encoded: {
                  stringValue: encodedImage
                }
              }
            }
          }
        }
      }
    };

    const request = {
      endpoint,
      instances: [instance],
    };

    // Call the Vertex AI API
    const client = getClient();
    const [prediction] = await client.predict(request);

    if (!prediction.predictions || prediction.predictions.length === 0) {
      throw new Error("No predictions returned from Vertex AI");
    }

    // Extract the embedding from the response
    const predictionData = prediction.predictions[0];
    const embedding = predictionData?.structValue?.fields?.imageEmbedding?.listValue?.values?.map(
      (v) => v.numberValue || 0
    );

    if (!embedding || !Array.isArray(embedding)) {
      throw new Error("Invalid embedding format returned from Vertex AI");
    }

    return embedding;
  } catch (error) {
    console.error("Error generating image embedding:", error);
    throw new Error(`Failed to generate image embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Calculate cosine similarity between two embeddings
 * 
 * @param embedding1 - First embedding vector
 * @param embedding2 - Second embedding vector
 * @returns number - Similarity score between 0 and 1
 */
export function calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number {
  if (embedding1.length !== embedding2.length) {
    throw new Error("Embeddings must have the same length");
  }

  let dotProduct = 0;
  let norm1 = 0;
  let norm2 = 0;

  for (let i = 0; i < embedding1.length; i++) {
    dotProduct += embedding1[i] * embedding2[i];
    norm1 += embedding1[i] * embedding1[i];
    norm2 += embedding2[i] * embedding2[i];
  }

  const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);
  
  if (magnitude === 0) {
    return 0;
  }

  return dotProduct / magnitude;
}

/**
 * Find similar products based on image embeddings
 * 
 * @param targetEmbedding - The embedding to compare against
 * @param productEmbeddings - Array of products with their embeddings
 * @param limit - Maximum number of similar products to return
 * @param threshold - Minimum similarity threshold (0-1)
 * @returns Array of similar products with similarity scores
 */
export function findSimilarProducts(
  targetEmbedding: number[],
  productEmbeddings: Array<{ id: string; embedding: number[]; title: string }>,
  limit: number = 10,
  threshold: number = 0.7
): Array<{ id: string; title: string; similarity: number }> {
  const similarities = productEmbeddings
    .map(product => ({
      id: product.id,
      title: product.title,
      similarity: calculateCosineSimilarity(targetEmbedding, product.embedding),
    }))
    .filter(item => item.similarity >= threshold)
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, limit);

  return similarities;
}

/**
 * Batch process multiple images for embedding generation
 * 
 * @param imageUrls - Array of image URLs to process
 * @param batchSize - Number of images to process concurrently
 * @returns Promise<Array<{ url: string; embedding: number[] | null; error?: string }>>
 */
export async function batchGenerateEmbeddings(
  imageUrls: string[],
  batchSize: number = 5
): Promise<Array<{ url: string; embedding: number[] | null; error?: string }>> {
  const results: Array<{ url: string; embedding: number[] | null; error?: string }> = [];

  // Process images in batches to avoid overwhelming the API
  for (let i = 0; i < imageUrls.length; i += batchSize) {
    const batch = imageUrls.slice(i, i + batchSize);
    
    const batchPromises = batch.map(async (url) => {
      try {
        const embedding = await generateImageEmbedding(url);
        return { url, embedding, error: undefined };
      } catch (error) {
        console.error(`Failed to process image ${url}:`, error);
        return { 
          url, 
          embedding: null, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);

    // Add a small delay between batches to be respectful to the API
    if (i + batchSize < imageUrls.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return results;
}

/**
 * Validate that the Google Cloud configuration is properly set up
 * 
 * @returns Promise<boolean> - True if configuration is valid
 */
export async function validateConfiguration(): Promise<boolean> {
  try {
    if (!EMBEDDING_CONFIG.projectId) {
      console.error("GCP_PROJECT_ID environment variable is not set");
      return false;
    }

    if (!process.env.GOOGLE_APPLICATION_CREDENTIALS) {
      console.error("GOOGLE_APPLICATION_CREDENTIALS environment variable is not set");
      return false;
    }

    // Try to initialize the client
    getClient();

    // This is a simple validation - in production you might want to make a test API call
    return true;
  } catch (error) {
    console.error("Google Cloud configuration validation failed:", error);
    return false;
  }
}

/**
 * Get embedding statistics for analysis
 * 
 * @param embeddings - Array of embeddings to analyze
 * @returns Object with statistics about the embeddings
 */
export function getEmbeddingStats(embeddings: number[][]): {
  count: number;
  dimensions: number;
  avgMagnitude: number;
  minValue: number;
  maxValue: number;
} {
  if (embeddings.length === 0) {
    return {
      count: 0,
      dimensions: 0,
      avgMagnitude: 0,
      minValue: 0,
      maxValue: 0,
    };
  }

  const dimensions = embeddings[0].length;
  let totalMagnitude = 0;
  let minValue = Infinity;
  let maxValue = -Infinity;

  embeddings.forEach(embedding => {
    // Calculate magnitude
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    totalMagnitude += magnitude;

    // Track min/max values
    embedding.forEach(val => {
      minValue = Math.min(minValue, val);
      maxValue = Math.max(maxValue, val);
    });
  });

  return {
    count: embeddings.length,
    dimensions,
    avgMagnitude: totalMagnitude / embeddings.length,
    minValue,
    maxValue,
  };
}
