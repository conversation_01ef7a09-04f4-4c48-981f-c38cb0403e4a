import { PredictionServiceClient } from '@google-cloud/aiplatform';

// Initialize the Google AI Platform client
let client: PredictionServiceClient | null = null;

function getClient(): PredictionServiceClient {
  if (!client) {
    client = new PredictionServiceClient({
      credentials: {
        client_email: process.env.GOOGLE_CLOUD_CLIENT_EMAIL,
        private_key: process.env.GOOGLE_CLOUD_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
    });
  }
  return client;
}

/**
 * Configuration for the text embedding service
 */
const EMBEDDING_CONFIG = {
  projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
  location: process.env.GOOGLE_CLOUD_LOCATION || 'us-central1',
  model: 'text-embedding-004',
};

/**
 * Generate text embeddings using Google Vertex AI
 */
export async function generateTextEmbedding(text: string): Promise<number[]> {
  // Validate environment variables
  if (!EMBEDDING_CONFIG.projectId) {
    throw new Error('GOOGLE_CLOUD_PROJECT_ID environment variable is required');
  }

  // Validate input
  if (!text || text.trim().length === 0) {
    throw new Error('Text input is required and cannot be empty');
  }

  if (text.length > 10000) {
    throw new Error('Text input is too long. Maximum length is 10,000 characters.');
  }

  try {
    // Prepare the request for Vertex AI
    const endpoint = `projects/${EMBEDDING_CONFIG.projectId}/locations/${EMBEDDING_CONFIG.location}/publishers/google/models/${EMBEDDING_CONFIG.model}`;

    const instance = {
      structValue: {
        fields: {
          content: {
            stringValue: text,
          },
        },
      },
    };

    const request = {
      endpoint,
      instances: [instance],
    };

    // Call the Vertex AI API
    const client = getClient();
    const [prediction] = await client.predict(request);

    if (!prediction.predictions || prediction.predictions.length === 0) {
      throw new Error('Failed to generate text embedding: No predictions returned');
    }

    // Extract the embedding from the response
    const predictionData = prediction.predictions[0];
    const embedding = predictionData?.structValue?.fields?.embeddings?.structValue?.fields?.values?.listValue?.values?.map(
      (v) => v.numberValue || 0
    );

    // Validate embedding
    if (!Array.isArray(embedding) || embedding.length === 0) {
      throw new Error('Invalid embedding format received from Vertex AI');
    }

    // Ensure all values are numbers
    const validEmbedding = embedding.map((value, index) => {
      if (typeof value !== 'number' || isNaN(value)) {
        throw new Error(`Invalid embedding value at index ${index}: ${value}`);
      }
      return value;
    });

    console.log(`Generated text embedding with ${validEmbedding.length} dimensions for text: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);

    return validEmbedding;
  } catch (error) {
    console.error('Error generating text embedding:', error);
    
    if (error instanceof Error) {
      // Re-throw with more context
      throw new Error(`Text embedding generation failed: ${error.message}`);
    }
    
    throw new Error('Unknown error occurred during text embedding generation');
  }
}

/**
 * Generate embeddings for multiple texts in batch
 */
export async function generateTextEmbeddingsBatch(texts: string[]): Promise<number[][]> {
  if (!texts || texts.length === 0) {
    throw new Error('At least one text is required');
  }

  if (texts.length > 100) {
    throw new Error('Maximum 100 texts allowed per batch');
  }

  // Validate all texts
  for (let i = 0; i < texts.length; i++) {
    const text = texts[i];
    if (!text || text.trim().length === 0) {
      throw new Error(`Text at index ${i} is empty or invalid`);
    }
    if (text.length > 10000) {
      throw new Error(`Text at index ${i} is too long. Maximum length is 10,000 characters.`);
    }
  }

  try {
    // Process texts in parallel with a reasonable concurrency limit
    const concurrencyLimit = 5;
    const results: number[][] = [];
    
    for (let i = 0; i < texts.length; i += concurrencyLimit) {
      const batch = texts.slice(i, i + concurrencyLimit);
      const batchPromises = batch.map(text => generateTextEmbedding(text));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    console.log(`Generated ${results.length} text embeddings in batch`);
    return results;
  } catch (error) {
    console.error('Error generating text embeddings batch:', error);
    throw error;
  }
}

/**
 * Calculate cosine similarity between two text embeddings
 */
export function calculateTextSimilarity(embedding1: number[], embedding2: number[]): number {
  if (embedding1.length !== embedding2.length) {
    throw new Error('Embeddings must have the same dimensions');
  }

  let dotProduct = 0;
  let norm1 = 0;
  let norm2 = 0;

  for (let i = 0; i < embedding1.length; i++) {
    dotProduct += embedding1[i] * embedding2[i];
    norm1 += embedding1[i] * embedding1[i];
    norm2 += embedding2[i] * embedding2[i];
  }

  const magnitude1 = Math.sqrt(norm1);
  const magnitude2 = Math.sqrt(norm2);

  if (magnitude1 === 0 || magnitude2 === 0) {
    return 0;
  }

  return dotProduct / (magnitude1 * magnitude2);
}

/**
 * Find the most similar texts to a query text from a collection
 */
export function findSimilarTexts(
  queryEmbedding: number[],
  textEmbeddings: { text: string; embedding: number[] }[],
  topK: number = 5
): { text: string; similarity: number }[] {
  const similarities = textEmbeddings.map(({ text, embedding }) => ({
    text,
    similarity: calculateTextSimilarity(queryEmbedding, embedding),
  }));

  return similarities
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, topK);
}
