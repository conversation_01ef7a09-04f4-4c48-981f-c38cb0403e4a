// Environment variable validation and type safety
// This ensures all required environment variables are present and properly typed

interface EnvironmentConfig {
  // Convex
  CONVEX_DEPLOYMENT: string;
  NEXT_PUBLIC_CONVEX_URL: string;
  
  // Redis (Required for production rate limiting and caching)
  UPSTASH_REDIS_REST_URL: string;
  UPSTASH_REDIS_REST_TOKEN: string;
  
  // Next.js
  NEXT_PUBLIC_APP_URL: string;
  
  // Security
  NEXTAUTH_SECRET?: string;
  NEXTAUTH_URL?: string;
  
  // Optional
  VERCEL_ANALYTICS_ID?: string;
  NODE_ENV: 'development' | 'production' | 'test';
}

function validateEnvironment(): EnvironmentConfig {
  const env = process.env;
  
  // Required variables
  const required = {
    CONVEX_DEPLOYMENT: env.CONVEX_DEPLOYMENT,
    NEXT_PUBLIC_CONVEX_URL: env.NEXT_PUBLIC_CONVEX_URL,
    UPSTASH_REDIS_REST_URL: env.UPSTASH_REDIS_REST_URL,
    UPSTASH_REDIS_REST_TOKEN: env.UPSTASH_REDIS_REST_TOKEN,
    NEXT_PUBLIC_APP_URL: env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    NODE_ENV: (env.NODE_ENV as any) || 'development',
  };
  
  // Check for missing required variables
  const missing = Object.entries(required)
    .filter(([key, value]) => !value)
    .map(([key]) => key);
  
  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}\n` +
      'Please check your .env.local file and ensure all required variables are set.\n' +
      'See .env.example for reference.'
    );
  }
  
  return {
    ...required,
    // Optional variables
    NEXTAUTH_SECRET: env.NEXTAUTH_SECRET,
    NEXTAUTH_URL: env.NEXTAUTH_URL,
    VERCEL_ANALYTICS_ID: env.VERCEL_ANALYTICS_ID,
  } as EnvironmentConfig;
}

// Validate environment on module load
export const env = validateEnvironment();

// Helper functions
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';

// Redis configuration check
export const hasRedisConfig = !!(env.UPSTASH_REDIS_REST_URL && env.UPSTASH_REDIS_REST_TOKEN);

if (isProduction && !hasRedisConfig) {
  console.warn(
    '⚠️  WARNING: Redis configuration is missing in production environment.\n' +
    'Rate limiting and caching will not work properly.\n' +
    'Please configure UPSTASH_REDIS_REST_URL and UPSTASH_REDIS_REST_TOKEN.'
  );
}
