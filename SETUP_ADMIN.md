# Setup First Super Admin

This guide explains how to create the first super admin account for your MaoMao application.

## Prerequisites

- The application must be running
- You must have a user account (created through the sign-in process)
- No super admin should exist yet

## Method 1: Web Interface (Recommended)

1. **Sign in to your account** at `/signin`
2. **Navigate to the setup page** at `/setup`
3. **Enter your email address** (the one you used to sign in)
4. **Click "Create Super Admin Account"**
5. **You'll be redirected to the admin dashboard**

## Method 2: Command Line Script

1. **Install dependencies** (if not already installed):
   ```bash
   npm install
   ```

2. **Run the setup script**:
   ```bash
   node scripts/make-super-admin.js <EMAIL>
   ```

   Replace `<EMAIL>` with the email address you used to sign in.

## What happens during setup?

- The script checks if a super admin already exists
- If not, it creates a new admin user record with `super_admin` role
- The account gets all available permissions
- You can then access the admin dashboard at `/admin`

## Super Admin Permissions

The super admin account includes all permissions:
- Product management (view, create, edit, delete)
- Order management (view, edit, assign, communicate)
- User management (view, edit, delete)
- Admin user management (view, create, edit, delete)
- Supplier management
- Group buy management
- Analytics and reporting
- System settings and logs

## Troubleshooting

### "Super admin already exists"
- A super admin has already been created
- Visit `/admin` to manage existing admin users

### "User with email must be created first"
- Make sure you've signed up/in with this email address
- Visit `/signin` to create an account first

### Permission denied errors
- Make sure you're signed in with the same email you're trying to promote
- Check that no super admin exists yet

## Security Notes

- Only one super admin can be created using this method
- Additional admin users must be created by the super admin through the admin dashboard
- The super admin account has full system access - keep the credentials secure