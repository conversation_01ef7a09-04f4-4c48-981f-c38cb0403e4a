import {
  rateLimiters,
  sanitizeInput,
  escapeHtml,
  validatePasswordStrength,
  generateSecureToken,
  validateFileUpload,
  AUDIT_ACTIONS
} from '@/lib/security/utils'

// Mock Upstash Redis for testing
jest.mock('@upstash/ratelimit', () => {
  return {
    Ratelimit: jest.fn().mockImplementation(() => ({
      limit: jest.fn().mockResolvedValue({ success: true, remaining: 10, reset: Date.now() + 60000 }),
    })),
  };
});

jest.mock('@/lib/redis', () => ({
  redis: {
    hgetall: jest.fn(),
    hset: jest.fn(),
    expire: jest.fn(),
    hincrby: jest.fn(),
  },
}));

describe('Security Utils', () => {
  describe('Rate Limiters', () => {
    it('should have all required rate limiters configured', () => {
      expect(rateLimiters.auth).toBeDefined()
      expect(rateLimiters.api_general).toBeDefined()
      expect(rateLimiters.api_strict).toBeDefined()
      expect(rateLimiters.admin_actions).toBeDefined()
    })

    it('should have proper rate limiter configuration', () => {
      // Test that rate limiters are properly instantiated
      // In a real test environment, you would test the actual rate limiting behavior
      // but for unit tests, we just verify the structure exists
      expect(typeof rateLimiters.auth.limit).toBe('function')
      expect(typeof rateLimiters.api_general.limit).toBe('function')
      expect(typeof rateLimiters.api_strict.limit).toBe('function')
      expect(typeof rateLimiters.admin_actions.limit).toBe('function')
    })
  })

  describe('Input Sanitization', () => {
    describe('sanitizeInput', () => {
      it('should remove dangerous characters', () => {
        expect(sanitizeInput('<script>alert("xss")</script>')).toBe('scriptalert("xss")/script')
        expect(sanitizeInput('Hello <world>')).toBe('Hello world')
        expect(sanitizeInput('javascript:alert(1)')).toBe('alert(1)')
      })

      it('should remove event handlers', () => {
        expect(sanitizeInput('onclick=alert(1)')).toBe('')
        expect(sanitizeInput('onload=malicious()')).toBe('')
        expect(sanitizeInput('onmouseover=hack()')).toBe('')
      })

      it('should trim whitespace', () => {
        expect(sanitizeInput('  hello world  ')).toBe('hello world')
      })
    })

    describe('escapeHtml', () => {
      it('should escape HTML special characters', () => {
        expect(escapeHtml('<script>')).toBe('&lt;script&gt;')
        expect(escapeHtml('Hello & "World"')).toBe('Hello &amp; &quot;World&quot;')
        expect(escapeHtml("It's a 'test'")).toBe('It&#39;s a &#39;test&#39;')
        expect(escapeHtml('Path/to/file')).toBe('Path&#x2F;to&#x2F;file')
      })
    })
  })

  describe('Password Strength Validation', () => {
    it('should validate strong passwords', () => {
      const result = validatePasswordStrength('StrongP@ssw0rd!')
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.score).toBe(4)
    })

    it('should reject weak passwords', () => {
      const result = validatePasswordStrength('weak')
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
      expect(result.score).toBeLessThan(4)
    })

    it('should require minimum length', () => {
      const result = validatePasswordStrength('Short1!')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Password must be at least 8 characters long')
    })

    it('should require lowercase letters', () => {
      const result = validatePasswordStrength('PASSWORD123!')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Password must contain at least one lowercase letter')
    })

    it('should require uppercase letters', () => {
      const result = validatePasswordStrength('password123!')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Password must contain at least one uppercase letter')
    })

    it('should require numbers', () => {
      const result = validatePasswordStrength('Password!')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Password must contain at least one number')
    })

    it('should require special characters', () => {
      const result = validatePasswordStrength('Password123')
      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Password must contain at least one special character')
    })
  })

  describe('Secure Token Generation', () => {
    it('should generate tokens of correct length', () => {
      expect(generateSecureToken(16)).toHaveLength(16)
      expect(generateSecureToken(32)).toHaveLength(32)
      expect(generateSecureToken()).toHaveLength(32) // default
    })

    it('should generate unique tokens', () => {
      const token1 = generateSecureToken()
      const token2 = generateSecureToken()
      expect(token1).not.toBe(token2)
    })

    it('should only contain safe characters', () => {
      const token = generateSecureToken(100)
      expect(token).toMatch(/^[A-Za-z0-9]+$/)
    })
  })

  describe('File Upload Validation', () => {
    const createMockFile = (name: string, size: number, type: string): File => {
      const file = {
        name,
        size,
        type,
        lastModified: Date.now(),
        webkitRelativePath: '',
        stream: () => new ReadableStream(),
        text: () => Promise.resolve(''),
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
        slice: () => new Blob(),
      } as File;
      return file;
    }

    it('should validate file size', () => {
      const smallFile = createMockFile('test.jpg', 1024, 'image/jpeg')
      const largeFile = createMockFile('large.jpg', 10 * 1024 * 1024, 'image/jpeg')

      expect(validateFileUpload(smallFile, { maxSize: 5 * 1024 * 1024 }).isValid).toBe(true)
      expect(validateFileUpload(largeFile, { maxSize: 5 * 1024 * 1024 }).isValid).toBe(false)
    })

    it('should validate file types', () => {
      const imageFile = createMockFile('test.jpg', 1024, 'image/jpeg')
      const textFile = createMockFile('test.txt', 1024, 'text/plain')

      const options = { allowedTypes: ['image/jpeg', 'image/png'] }
      
      expect(validateFileUpload(imageFile, options).isValid).toBe(true)
      expect(validateFileUpload(textFile, options).isValid).toBe(false)
    })

    it('should validate file extensions', () => {
      const jpgFile = createMockFile('test.jpg', 1024, 'image/jpeg')
      const txtFile = createMockFile('test.txt', 1024, 'text/plain')

      const options = { allowedExtensions: ['jpg', 'png', 'gif'] }
      
      expect(validateFileUpload(jpgFile, options).isValid).toBe(true)
      expect(validateFileUpload(txtFile, options).isValid).toBe(false)
    })

    it('should provide appropriate error messages', () => {
      const largeFile = createMockFile('large.jpg', 10 * 1024 * 1024, 'image/jpeg')
      const result = validateFileUpload(largeFile, { maxSize: 5 * 1024 * 1024 })
      
      expect(result.isValid).toBe(false)
      expect(result.error).toContain('File size must be less than')
    })
  })

  describe('Audit Actions', () => {
    it('should define all necessary audit action types', () => {
      expect(AUDIT_ACTIONS.LOGIN).toBe('login')
      expect(AUDIT_ACTIONS.LOGOUT).toBe('logout')
      expect(AUDIT_ACTIONS.USER_CREATED).toBe('user_created')
      expect(AUDIT_ACTIONS.PRODUCT_CREATED).toBe('product_created')
      expect(AUDIT_ACTIONS.ORDER_CREATED).toBe('order_created')
      expect(AUDIT_ACTIONS.ADMIN_CREATED).toBe('admin_created')
      expect(AUDIT_ACTIONS.SYSTEM_SETTINGS_CHANGED).toBe('system_settings_changed')
    })

    it('should have consistent naming convention', () => {
      Object.values(AUDIT_ACTIONS).forEach(action => {
        expect(action).toMatch(/^[a-z_]+$/)
      })
    })
  })
})
