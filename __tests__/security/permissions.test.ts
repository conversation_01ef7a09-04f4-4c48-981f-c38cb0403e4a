import { 
  PERMISSIONS, 
  ROLE_PERMISSIONS, 
  getPermissionsForRole, 
  roleHasPermission 
} from '@/convex/auth/permissions'

describe('Permission System', () => {
  describe('PERMISSIONS constant', () => {
    it('should have all required permission categories', () => {
      const permissionKeys = Object.keys(PERMISSIONS)
      
      // Check for main categories
      expect(permissionKeys.some(key => key.startsWith('PRODUCTS_'))).toBe(true)
      expect(permissionKeys.some(key => key.startsWith('ORDERS_'))).toBe(true)
      expect(permissionKeys.some(key => key.startsWith('USERS_'))).toBe(true)
      expect(permissionKeys.some(key => key.startsWith('ADMIN_USERS_'))).toBe(true)
      expect(permissionKeys.some(key => key.startsWith('SUPPLIERS_'))).toBe(true)
      expect(permissionKeys.some(key => key.startsWith('GROUP_BUYS_'))).toBe(true)
      expect(permissionKeys.some(key => key.startsWith('ANALYTICS_'))).toBe(true)
      expect(permissionKeys.some(key => key.startsWith('SYSTEM_'))).toBe(true)
    })

    it('should have consistent permission naming', () => {
      Object.values(PERMISSIONS).forEach(permission => {
        expect(permission).toMatch(/^[a-z_]+:[a-z_]+$/)
      })
    })
  })

  describe('ROLE_PERMISSIONS', () => {
    it('should define permissions for all roles', () => {
      expect(ROLE_PERMISSIONS.super_admin).toBeDefined()
      expect(ROLE_PERMISSIONS.admin).toBeDefined()
      expect(ROLE_PERMISSIONS.moderator).toBeDefined()
    })

    it('should give super_admin all permissions', () => {
      const allPermissions = Object.values(PERMISSIONS)
      const superAdminPermissions = ROLE_PERMISSIONS.super_admin
      
      allPermissions.forEach(permission => {
        expect(superAdminPermissions).toContain(permission)
      })
    })

    it('should give admin fewer permissions than super_admin', () => {
      const superAdminPermissions = ROLE_PERMISSIONS.super_admin
      const adminPermissions = ROLE_PERMISSIONS.admin
      
      expect(adminPermissions.length).toBeLessThan(superAdminPermissions.length)
    })

    it('should give moderator fewer permissions than admin', () => {
      const adminPermissions = ROLE_PERMISSIONS.admin
      const moderatorPermissions = ROLE_PERMISSIONS.moderator
      
      expect(moderatorPermissions.length).toBeLessThan(adminPermissions.length)
    })

    it('should not allow moderators to delete or create critical resources', () => {
      const moderatorPermissions = ROLE_PERMISSIONS.moderator
      
      expect(moderatorPermissions).not.toContain(PERMISSIONS.PRODUCTS_DELETE)
      expect(moderatorPermissions).not.toContain(PERMISSIONS.ADMIN_USERS_CREATE)
      expect(moderatorPermissions).not.toContain(PERMISSIONS.ADMIN_USERS_DELETE)
      expect(moderatorPermissions).not.toContain(PERMISSIONS.SYSTEM_SETTINGS)
    })
  })

  describe('getPermissionsForRole', () => {
    it('should return correct permissions for super_admin', () => {
      const permissions = getPermissionsForRole('super_admin')
      expect(permissions).toEqual(ROLE_PERMISSIONS.super_admin)
    })

    it('should return correct permissions for admin', () => {
      const permissions = getPermissionsForRole('admin')
      expect(permissions).toEqual(ROLE_PERMISSIONS.admin)
    })

    it('should return correct permissions for moderator', () => {
      const permissions = getPermissionsForRole('moderator')
      expect(permissions).toEqual(ROLE_PERMISSIONS.moderator)
    })
  })

  describe('roleHasPermission', () => {
    it('should return true when role has permission', () => {
      expect(roleHasPermission('super_admin', PERMISSIONS.PRODUCTS_CREATE)).toBe(true)
      expect(roleHasPermission('admin', PERMISSIONS.PRODUCTS_CREATE)).toBe(true)
      expect(roleHasPermission('moderator', PERMISSIONS.PRODUCTS_VIEW)).toBe(true)
    })

    it('should return false when role does not have permission', () => {
      expect(roleHasPermission('moderator', PERMISSIONS.PRODUCTS_DELETE)).toBe(false)
      expect(roleHasPermission('moderator', PERMISSIONS.ADMIN_USERS_CREATE)).toBe(false)
      expect(roleHasPermission('admin', PERMISSIONS.SYSTEM_SETTINGS)).toBe(false)
    })
  })

  describe('Permission hierarchy', () => {
    it('should ensure super_admin can do everything admin can do', () => {
      const adminPermissions = ROLE_PERMISSIONS.admin
      const superAdminPermissions = ROLE_PERMISSIONS.super_admin
      
      adminPermissions.forEach(permission => {
        expect(superAdminPermissions).toContain(permission)
      })
    })

    it('should ensure admin can do everything moderator can do', () => {
      const moderatorPermissions = ROLE_PERMISSIONS.moderator
      const adminPermissions = ROLE_PERMISSIONS.admin
      
      moderatorPermissions.forEach(permission => {
        expect(adminPermissions).toContain(permission)
      })
    })
  })

  describe('Critical permissions', () => {
    it('should restrict admin user management to super_admin only', () => {
      expect(ROLE_PERMISSIONS.super_admin).toContain(PERMISSIONS.ADMIN_USERS_CREATE)
      expect(ROLE_PERMISSIONS.super_admin).toContain(PERMISSIONS.ADMIN_USERS_DELETE)
      
      expect(ROLE_PERMISSIONS.admin).not.toContain(PERMISSIONS.ADMIN_USERS_CREATE)
      expect(ROLE_PERMISSIONS.admin).not.toContain(PERMISSIONS.ADMIN_USERS_DELETE)
      
      expect(ROLE_PERMISSIONS.moderator).not.toContain(PERMISSIONS.ADMIN_USERS_CREATE)
      expect(ROLE_PERMISSIONS.moderator).not.toContain(PERMISSIONS.ADMIN_USERS_DELETE)
    })

    it('should restrict system settings to super_admin only', () => {
      expect(ROLE_PERMISSIONS.super_admin).toContain(PERMISSIONS.SYSTEM_SETTINGS)
      expect(ROLE_PERMISSIONS.admin).not.toContain(PERMISSIONS.SYSTEM_SETTINGS)
      expect(ROLE_PERMISSIONS.moderator).not.toContain(PERMISSIONS.SYSTEM_SETTINGS)
    })

    it('should allow all roles to view basic resources', () => {
      const viewPermissions = [
        PERMISSIONS.PRODUCTS_VIEW,
        PERMISSIONS.ORDERS_VIEW,
        PERMISSIONS.USERS_VIEW,
        PERMISSIONS.SUPPLIERS_VIEW,
        PERMISSIONS.GROUP_BUYS_VIEW,
        PERMISSIONS.ANALYTICS_VIEW,
      ]

      viewPermissions.forEach(permission => {
        expect(ROLE_PERMISSIONS.super_admin).toContain(permission)
        expect(ROLE_PERMISSIONS.admin).toContain(permission)
        expect(ROLE_PERMISSIONS.moderator).toContain(permission)
      })
    })
  })
})
