import { 
  transformProductToSummary, 
  transformOrderToSummary, 
  transformStatsToDashboardMetrics,
  applySearchFilters,
  applySorting,
  applyPagination,
  type SearchFilters,
  type SortDirection 
} from '@/lib/business/dataTransforms';

describe('Database Integration Tests', () => {
  describe('Data Transformations', () => {
    it('should transform product data correctly', () => {
      const mockProduct = {
        _id: 'product_123',
        title: 'Test Product',
        finalPrice: 29.99,
        stockCount: 50,
        status: 'active',
        images: ['image1.jpg', 'image2.jpg'],
        supplier: {
          id: 'supplier_123',
          name: 'Test Supplier',
        },
      };

      const summary = transformProductToSummary(mockProduct);

      expect(summary.id).toBe('product_123');
      expect(summary.title).toBe('Test Product');
      expect(summary.finalPrice).toBe(29.99);
      expect(summary.stockCount).toBe(50);
      expect(summary.status).toBe('active');
      expect(summary.images).toHaveLength(2);
      expect(summary.supplier?.name).toBe('Test Supplier');
    });

    it('should transform order data correctly', () => {
      const mockOrder = {
        _id: 'order_123',
        status: 'shipped',
        totalAmount: 59.98,
        items: [
          { productId: 'product_1', quantity: 2 },
          { productId: 'product_2', quantity: 1 },
        ],
        user: {
          name: 'John Doe',
          email: '<EMAIL>',
        },
        _creationTime: Date.now(),
        assignedUser: {
          id: 'admin_123',
          name: 'Admin User',
        },
      };

      const summary = transformOrderToSummary(mockOrder);

      expect(summary.id).toBe('order_123');
      expect(summary.status).toBe('shipped');
      expect(summary.totalAmount).toBe(59.98);
      expect(summary.itemCount).toBe(2);
      expect(summary.customerName).toBe('John Doe');
      expect(summary.customerEmail).toBe('<EMAIL>');
      expect(summary.assignedTo?.name).toBe('Admin User');
    });

    it('should transform stats to dashboard metrics', () => {
      const mockStats = {
        orderStats: {
          total: 100,
          new: 10,
          actionRequired: 5,
          shipped: 20,
          delivered: 60,
          totalRevenue: 5000,
        },
        productStats: {
          total: 200,
          active: 180,
          lowStock: 15,
          outOfStock: 5,
        },
        supplierStats: {
          total: 25,
          active: 23,
        },
        groupBuyStats: {
          active: 3,
          completed: 12,
          totalParticipants: 150,
        },
      };

      const metrics = transformStatsToDashboardMetrics(mockStats);

      expect(metrics.orders.total).toBe(100);
      expect(metrics.orders.revenue).toBe(5000);
      expect(metrics.products.total).toBe(200);
      expect(metrics.products.active).toBe(180);
      expect(metrics.suppliers.total).toBe(25);
      expect(metrics.groupBuys.active).toBe(3);
    });
  });

  describe('Search and Filtering', () => {
    const mockProducts = [
      {
        _id: 'product_1',
        title: 'iPhone 15 Pro',
        description: 'Latest Apple smartphone',
        status: 'active',
        finalPrice: 999,
        tags: ['electronics', 'phone', 'apple'],
        supplierId: 'supplier_1',
        _creationTime: Date.now() - (7 * 24 * 60 * 60 * 1000), // 7 days ago
      },
      {
        _id: 'product_2',
        title: 'Samsung Galaxy S24',
        description: 'Android flagship phone',
        status: 'active',
        finalPrice: 899,
        tags: ['electronics', 'phone', 'samsung'],
        supplierId: 'supplier_2',
        _creationTime: Date.now() - (3 * 24 * 60 * 60 * 1000), // 3 days ago
      },
      {
        _id: 'product_3',
        title: 'MacBook Pro',
        description: 'Professional laptop',
        status: 'inactive',
        finalPrice: 1999,
        tags: ['electronics', 'laptop', 'apple'],
        supplierId: 'supplier_1',
        _creationTime: Date.now() - (1 * 24 * 60 * 60 * 1000), // 1 day ago
      },
    ];

    it('should filter by text search', () => {
      const filters: SearchFilters = {
        query: 'iPhone',
      };

      const results = applySearchFilters(mockProducts, filters, ['title', 'description']);
      
      expect(results).toHaveLength(1);
      expect(results[0].title).toBe('iPhone 15 Pro');
    });

    it('should filter by status', () => {
      const filters: SearchFilters = {
        status: ['active'],
      };

      const results = applySearchFilters(mockProducts, filters);
      
      expect(results).toHaveLength(2);
      expect(results.every(p => p.status === 'active')).toBe(true);
    });

    it('should filter by price range', () => {
      const filters: SearchFilters = {
        priceRange: {
          min: 800,
          max: 1000,
        },
      };

      const results = applySearchFilters(mockProducts, filters);
      
      expect(results).toHaveLength(2);
      expect(results.every(p => p.finalPrice >= 800 && p.finalPrice <= 1000)).toBe(true);
    });

    it('should filter by tags', () => {
      const filters: SearchFilters = {
        tags: ['apple'],
      };

      const results = applySearchFilters(mockProducts, filters);
      
      expect(results).toHaveLength(2);
      expect(results.every(p => p.tags.includes('apple'))).toBe(true);
    });

    it('should filter by supplier', () => {
      const filters: SearchFilters = {
        supplierId: 'supplier_1' as any,
      };

      const results = applySearchFilters(mockProducts, filters);
      
      expect(results).toHaveLength(2);
      expect(results.every(p => p.supplierId === 'supplier_1')).toBe(true);
    });

    it('should combine multiple filters', () => {
      const filters: SearchFilters = {
        status: ['active'],
        tags: ['phone'],
        priceRange: {
          min: 800,
          max: 1000,
        },
      };

      const results = applySearchFilters(mockProducts, filters);
      
      expect(results).toHaveLength(2);
      expect(results.every(p => 
        p.status === 'active' && 
        p.tags.includes('phone') && 
        p.finalPrice >= 800 && 
        p.finalPrice <= 1000
      )).toBe(true);
    });
  });

  describe('Sorting', () => {
    const mockData = [
      { id: 1, name: 'Charlie', price: 100, date: 3 },
      { id: 2, name: 'Alice', price: 200, date: 1 },
      { id: 3, name: 'Bob', price: 150, date: 2 },
    ];

    it('should sort by string field ascending', () => {
      const sorted = applySorting(mockData, 'name', 'asc');
      
      expect(sorted[0].name).toBe('Alice');
      expect(sorted[1].name).toBe('Bob');
      expect(sorted[2].name).toBe('Charlie');
    });

    it('should sort by string field descending', () => {
      const sorted = applySorting(mockData, 'name', 'desc');
      
      expect(sorted[0].name).toBe('Charlie');
      expect(sorted[1].name).toBe('Bob');
      expect(sorted[2].name).toBe('Alice');
    });

    it('should sort by number field ascending', () => {
      const sorted = applySorting(mockData, 'price', 'asc');
      
      expect(sorted[0].price).toBe(100);
      expect(sorted[1].price).toBe(150);
      expect(sorted[2].price).toBe(200);
    });

    it('should sort by number field descending', () => {
      const sorted = applySorting(mockData, 'price', 'desc');
      
      expect(sorted[0].price).toBe(200);
      expect(sorted[1].price).toBe(150);
      expect(sorted[2].price).toBe(100);
    });

    it('should handle null values in sorting', () => {
      const dataWithNulls = [
        { id: 1, name: 'Alice', value: 100 },
        { id: 2, name: null, value: 200 },
        { id: 3, name: 'Bob', value: null },
      ];

      const sorted = applySorting(dataWithNulls, 'name', 'asc');
      
      // Null values should be sorted to the end for ascending
      expect(sorted[0].name).toBe('Alice');
      expect(sorted[1].name).toBe('Bob');
      expect(sorted[2].name).toBe(null);
    });
  });

  describe('Pagination', () => {
    const mockData = Array.from({ length: 25 }, (_, i) => ({
      id: i + 1,
      name: `Item ${i + 1}`,
    }));

    it('should paginate data correctly', () => {
      const result = applyPagination(mockData, 1, 10);
      
      expect(result.items).toHaveLength(10);
      expect(result.totalCount).toBe(25);
      expect(result.totalPages).toBe(3);
      expect(result.currentPage).toBe(1);
      expect(result.hasNextPage).toBe(true);
      expect(result.hasPreviousPage).toBe(false);
      expect(result.items[0].id).toBe(1);
      expect(result.items[9].id).toBe(10);
    });

    it('should handle middle page correctly', () => {
      const result = applyPagination(mockData, 2, 10);
      
      expect(result.items).toHaveLength(10);
      expect(result.currentPage).toBe(2);
      expect(result.hasNextPage).toBe(true);
      expect(result.hasPreviousPage).toBe(true);
      expect(result.items[0].id).toBe(11);
      expect(result.items[9].id).toBe(20);
    });

    it('should handle last page correctly', () => {
      const result = applyPagination(mockData, 3, 10);
      
      expect(result.items).toHaveLength(5); // Only 5 items on last page
      expect(result.currentPage).toBe(3);
      expect(result.hasNextPage).toBe(false);
      expect(result.hasPreviousPage).toBe(true);
      expect(result.items[0].id).toBe(21);
      expect(result.items[4].id).toBe(25);
    });

    it('should handle page out of bounds', () => {
      const result = applyPagination(mockData, 10, 10); // Page 10 doesn't exist
      
      expect(result.currentPage).toBe(3); // Should clamp to last valid page
      expect(result.items).toHaveLength(5);
    });

    it('should handle empty data', () => {
      const result = applyPagination([], 1, 10);
      
      expect(result.items).toHaveLength(0);
      expect(result.totalCount).toBe(0);
      expect(result.totalPages).toBe(0);
      expect(result.hasNextPage).toBe(false);
      expect(result.hasPreviousPage).toBe(false);
    });
  });

  describe('Data Consistency Checks', () => {
    it('should maintain data consistency across transformations', () => {
      const originalData = [
        { id: 1, name: 'Product A', price: 100, status: 'active' },
        { id: 2, name: 'Product B', price: 200, status: 'inactive' },
        { id: 3, name: 'Product C', price: 150, status: 'active' },
      ];

      // Apply filters
      const filtered = applySearchFilters(originalData, { status: ['active'] });
      expect(filtered).toHaveLength(2);

      // Apply sorting
      const sorted = applySorting(filtered, 'price', 'asc');
      expect(sorted[0].price).toBe(100);
      expect(sorted[1].price).toBe(150);

      // Apply pagination
      const paginated = applyPagination(sorted, 1, 1);
      expect(paginated.items).toHaveLength(1);
      expect(paginated.items[0].id).toBe(1);
      expect(paginated.totalCount).toBe(2); // Total filtered count
    });

    it('should handle edge cases in data processing', () => {
      const edgeCaseData = [
        { id: 1, name: '', price: 0, status: null },
        { id: 2, name: null, price: -10, status: undefined },
        { id: 3, name: 'Valid', price: 100, status: 'active' },
      ];

      // Should handle null/undefined values gracefully
      const sorted = applySorting(edgeCaseData, 'name', 'asc');
      expect(sorted).toHaveLength(3);

      const paginated = applyPagination(sorted, 1, 10);
      expect(paginated.items).toHaveLength(3);
    });
  });

  describe('Performance Considerations', () => {
    it('should handle large datasets efficiently', () => {
      const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
        id: i + 1,
        name: `Item ${i + 1}`,
        price: Math.random() * 1000,
        status: i % 2 === 0 ? 'active' : 'inactive',
      }));

      const startTime = Date.now();

      // Apply multiple operations
      const filtered = applySearchFilters(largeDataset, { status: ['active'] });
      const sorted = applySorting(filtered, 'price', 'desc');
      const paginated = applyPagination(sorted, 1, 20);

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Should complete within reasonable time (adjust threshold as needed)
      expect(processingTime).toBeLessThan(1000); // 1 second
      expect(paginated.items).toHaveLength(20);
      expect(paginated.totalCount).toBe(5000); // Half should be active
    });
  });
});
