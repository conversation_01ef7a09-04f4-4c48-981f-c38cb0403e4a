import { 
  isValidStatusTransition, 
  getValidNextStatuses, 
  validateStatusTransition,
  getStatusDisplayInfo,
  calculateProcessingTime,
  getOrdersNeedingAttention,
  type OrderStatus 
} from '@/lib/business/orderStatus';
import { validateFields, validate } from '@/lib/validation/schemas';

describe('Order Database Operations', () => {
  describe('Order Status Transitions', () => {
    it('should allow valid status transitions', () => {
      const validTransitions: Array<[OrderStatus, OrderStatus]> = [
        ['new', 'sourcing'],
        ['new', 'cancelled'],
        ['sourcing', 'action_required'],
        ['sourcing', 'shipped'],
        ['sourcing', 'cancelled'],
        ['action_required', 'sourcing'],
        ['action_required', 'shipped'],
        ['action_required', 'cancelled'],
        ['shipped', 'delivered'],
        ['shipped', 'action_required'],
      ];

      validTransitions.forEach(([from, to]) => {
        expect(isValidStatusTransition(from, to)).toBe(true);
      });
    });

    it('should reject invalid status transitions', () => {
      const invalidTransitions: Array<[OrderStatus, OrderStatus]> = [
        ['delivered', 'shipped'], // Cannot go back from delivered
        ['cancelled', 'new'], // Cannot go back from cancelled
        ['new', 'delivered'], // Cannot skip to delivered
        ['sourcing', 'delivered'], // Cannot skip shipped
        ['delivered', 'cancelled'], // Cannot cancel delivered order
      ];

      invalidTransitions.forEach(([from, to]) => {
        expect(isValidStatusTransition(from, to)).toBe(false);
      });
    });

    it('should return correct next valid statuses', () => {
      expect(getValidNextStatuses('new')).toEqual(['sourcing', 'cancelled']);
      expect(getValidNextStatuses('sourcing')).toEqual(['action_required', 'shipped', 'cancelled']);
      expect(getValidNextStatuses('delivered')).toEqual([]); // Final state
      expect(getValidNextStatuses('cancelled')).toEqual([]); // Final state
    });

    it('should validate status transitions with required data', () => {
      // Transition to shipped requires tracking number
      const shippedTransition = validateStatusTransition('sourcing', 'shipped', {
        trackingNumber: 'TRACK123',
      });
      expect(shippedTransition.isValid).toBe(true);

      // Missing tracking number should fail
      const shippedWithoutTracking = validateStatusTransition('sourcing', 'shipped', {});
      expect(shippedWithoutTracking.isValid).toBe(false);
      expect(shippedWithoutTracking.errors).toContain('Tracking number is required when marking order as shipped');

      // Transition to action_required requires issue description
      const actionRequiredTransition = validateStatusTransition('sourcing', 'action_required', {
        issueDescription: 'Product out of stock',
      });
      expect(actionRequiredTransition.isValid).toBe(true);

      // Missing issue description should fail
      const actionRequiredWithoutIssue = validateStatusTransition('sourcing', 'action_required', {});
      expect(actionRequiredWithoutIssue.isValid).toBe(false);
      expect(actionRequiredWithoutIssue.errors).toContain('Issue description is required when marking order as action required');
    });

    it('should prevent cancellation of shipped/delivered orders', () => {
      const cancelShipped = validateStatusTransition('shipped', 'cancelled', {});
      expect(cancelShipped.isValid).toBe(false);
      expect(cancelShipped.errors).toContain('Invalid status transition from shipped to cancelled');

      const cancelDelivered = validateStatusTransition('delivered', 'cancelled', {});
      expect(cancelDelivered.isValid).toBe(false);
      expect(cancelDelivered.errors).toContain('Invalid status transition from delivered to cancelled');
    });
  });

  describe('Order Validation', () => {
    it('should validate order data structure', () => {
      const validOrder = {
        userId: 'user_123',
        items: [
          {
            productId: 'product_123',
            quantity: 2,
            priceAtTime: 29.99,
            title: 'Test Product',
          },
        ],
        shippingAddress: {
          name: 'John Doe',
          address: '123 Main St',
          city: 'New York',
          country: 'USA',
          postalCode: '10001',
        },
        totalAmount: 59.98,
        status: 'new' as OrderStatus,
      };

      const rules = {
        userId: (value: string) => validate.requiredString(value, 1),
        totalAmount: (value: number) => validate.price(value),
        items: (value: any[]) => validate.nonEmptyArray(value),
      };

      const result = validateFields(validOrder, rules);
      expect(result.isValid).toBe(true);
    });

    it('should validate order items', () => {
      const validItem = {
        productId: 'product_123',
        quantity: 2,
        priceAtTime: 29.99,
        title: 'Test Product',
      };

      const invalidItem = {
        productId: '',
        quantity: 0,
        priceAtTime: -10,
        title: '',
      };

      // Valid item
      expect(validate.requiredString(validItem.productId, 1)).toBe(true);
      expect(validItem.quantity > 0).toBe(true);
      expect(validate.price(validItem.priceAtTime)).toBe(true);
      expect(validate.requiredString(validItem.title, 1)).toBe(true);

      // Invalid item
      expect(validate.requiredString(invalidItem.productId, 1)).toBe(false);
      expect(invalidItem.quantity > 0).toBe(false);
      expect(validate.price(invalidItem.priceAtTime)).toBe(false);
      expect(validate.requiredString(invalidItem.title, 1)).toBe(false);
    });

    it('should validate shipping address', () => {
      const validAddress = {
        name: 'John Doe',
        address: '123 Main St',
        city: 'New York',
        country: 'USA',
        postalCode: '10001',
      };

      const invalidAddress = {
        name: '',
        address: '',
        city: '',
        country: '',
        postalCode: '',
      };

      // Valid address
      Object.values(validAddress).forEach(value => {
        expect(validate.requiredString(value, 1)).toBe(true);
      });

      // Invalid address
      Object.values(invalidAddress).forEach(value => {
        expect(validate.requiredString(value, 1)).toBe(false);
      });
    });
  });

  describe('Order Processing Time Calculations', () => {
    it('should calculate processing time correctly', () => {
      const createdAt = Date.now() - (2 * 24 * 60 * 60 * 1000); // 2 days ago
      const currentStatus: OrderStatus = 'sourcing';

      const result = calculateProcessingTime(createdAt, currentStatus);

      expect(result.totalProcessingTime).toBeGreaterThan(0);
      expect(result.timeInCurrentStatus).toBeGreaterThan(0);
      expect(result.averageTimePerStatus).toBeGreaterThan(0);
    });

    it('should calculate time with status history', () => {
      const createdAt = Date.now() - (3 * 24 * 60 * 60 * 1000); // 3 days ago
      const currentStatus: OrderStatus = 'shipped';
      const statusHistory = [
        { status: 'new' as OrderStatus, timestamp: createdAt },
        { status: 'sourcing' as OrderStatus, timestamp: createdAt + (24 * 60 * 60 * 1000) },
        { status: 'shipped' as OrderStatus, timestamp: createdAt + (2 * 24 * 60 * 60 * 1000) },
      ];

      const result = calculateProcessingTime(createdAt, currentStatus, statusHistory);

      expect(result.totalProcessingTime).toBeGreaterThan(0);
      expect(result.timeInCurrentStatus).toBeLessThan(result.totalProcessingTime);
      expect(result.averageTimePerStatus).toBeGreaterThan(0);
    });
  });

  describe('Orders Needing Attention', () => {
    it('should identify orders needing attention', () => {
      const now = Date.now();
      const oneDayMs = 24 * 60 * 60 * 1000;

      const orders = [
        {
          id: 'order_1',
          status: 'new' as OrderStatus,
          createdAt: now - (2 * oneDayMs), // 2 days old
        },
        {
          id: 'order_2',
          status: 'sourcing' as OrderStatus,
          createdAt: now - (8 * oneDayMs), // 8 days old
        },
        {
          id: 'order_3',
          status: 'action_required' as OrderStatus,
          createdAt: now - (4 * oneDayMs), // 4 days old
        },
        {
          id: 'order_4',
          status: 'delivered' as OrderStatus,
          createdAt: now - (10 * oneDayMs), // 10 days old but delivered
        },
      ];

      const needingAttention = getOrdersNeedingAttention(orders);

      // Should identify orders 1, 2, and 3 but not 4 (delivered)
      expect(needingAttention.length).toBe(3);
      
      // Order 2 (sourcing for 8 days) should be high priority
      const order2Attention = needingAttention.find(item => item.id === 'order_2');
      expect(order2Attention?.priority).toBe('high');

      // Order 3 (action_required for 4 days) should be high priority
      const order3Attention = needingAttention.find(item => item.id === 'order_3');
      expect(order3Attention?.priority).toBe('high');

      // Should not include delivered order
      const order4Attention = needingAttention.find(item => item.id === 'order_4');
      expect(order4Attention).toBeUndefined();
    });

    it('should sort orders by priority and days in status', () => {
      const now = Date.now();
      const oneDayMs = 24 * 60 * 60 * 1000;

      const orders = [
        {
          id: 'order_1',
          status: 'new' as OrderStatus,
          createdAt: now - (2 * oneDayMs), // 2 days - high priority
        },
        {
          id: 'order_2',
          status: 'sourcing' as OrderStatus,
          createdAt: now - (4 * oneDayMs), // 4 days - medium priority
        },
        {
          id: 'order_3',
          status: 'sourcing' as OrderStatus,
          createdAt: now - (8 * oneDayMs), // 8 days - high priority
        },
      ];

      const needingAttention = getOrdersNeedingAttention(orders);

      // Should be sorted by priority (high first) then by days in status (more days first)
      expect(needingAttention[0].priority).toBe('high');
      expect(needingAttention[0].daysInStatus).toBeGreaterThanOrEqual(needingAttention[1].daysInStatus);
    });
  });

  describe('Order Status Display Information', () => {
    it('should provide correct display information for each status', () => {
      const statuses: OrderStatus[] = ['new', 'sourcing', 'action_required', 'shipped', 'delivered', 'cancelled'];

      statuses.forEach(status => {
        const displayInfo = getStatusDisplayInfo(status);
        
        expect(displayInfo.label).toBeTruthy();
        expect(displayInfo.color).toBeTruthy();
        expect(displayInfo.icon).toBeTruthy();
        expect(displayInfo.description).toBeTruthy();
        expect(typeof displayInfo.isActive).toBe('boolean');
        expect(typeof displayInfo.isFinal).toBe('boolean');
      });

      // Final statuses should not be active
      expect(getStatusDisplayInfo('delivered').isFinal).toBe(true);
      expect(getStatusDisplayInfo('delivered').isActive).toBe(false);
      expect(getStatusDisplayInfo('cancelled').isFinal).toBe(true);
      expect(getStatusDisplayInfo('cancelled').isActive).toBe(false);

      // Active statuses should not be final
      expect(getStatusDisplayInfo('new').isActive).toBe(true);
      expect(getStatusDisplayInfo('new').isFinal).toBe(false);
      expect(getStatusDisplayInfo('sourcing').isActive).toBe(true);
      expect(getStatusDisplayInfo('sourcing').isFinal).toBe(false);
    });
  });

  describe('Order Data Integrity', () => {
    it('should maintain referential integrity with products', () => {
      // Mock order item validation
      const orderItem = {
        productId: 'valid_product_id',
        quantity: 2,
        priceAtTime: 29.99,
        title: 'Valid Product',
      };

      const invalidOrderItem = {
        productId: '', // Invalid product reference
        quantity: 2,
        priceAtTime: 29.99,
        title: 'Invalid Product',
      };

      expect(validate.requiredString(orderItem.productId, 1)).toBe(true);
      expect(validate.requiredString(invalidOrderItem.productId, 1)).toBe(false);
    });

    it('should maintain referential integrity with users', () => {
      // Mock user reference validation
      const validUserId = 'user_123';
      const invalidUserId = '';

      expect(validate.requiredString(validUserId, 1)).toBe(true);
      expect(validate.requiredString(invalidUserId, 1)).toBe(false);
    });

    it('should validate order total calculations', () => {
      const orderItems = [
        { quantity: 2, priceAtTime: 15.99 },
        { quantity: 1, priceAtTime: 29.99 },
      ];

      const calculatedTotal = orderItems.reduce(
        (sum, item) => sum + (item.quantity * item.priceAtTime), 
        0
      );

      const expectedTotal = (2 * 15.99) + (1 * 29.99); // 61.97

      expect(calculatedTotal).toBe(expectedTotal);
      expect(validate.price(calculatedTotal)).toBe(true);
    });

    it('should handle communication history integrity', () => {
      const validCommunication = {
        message: 'Order status updated',
        fromAdmin: true,
        adminUserId: 'admin_123',
        timestamp: Date.now(),
      };

      const invalidCommunication = {
        message: '', // Empty message
        fromAdmin: true,
        adminUserId: '', // Invalid admin reference
        timestamp: 0, // Invalid timestamp
      };

      expect(validate.requiredString(validCommunication.message, 1)).toBe(true);
      expect(validate.requiredString(validCommunication.adminUserId, 1)).toBe(true);
      expect(validCommunication.timestamp > 0).toBe(true);

      expect(validate.requiredString(invalidCommunication.message, 1)).toBe(false);
      expect(validate.requiredString(invalidCommunication.adminUserId, 1)).toBe(false);
      expect(invalidCommunication.timestamp > 0).toBe(false);
    });
  });
});
