"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import NewProductPage from "./dashboard/products/new/page";
import ProductDetailPage from "./dashboard/products/[id]/page";

export default function ProductRedesignPreview() {
  // Mock params for the detail page
  const mockParams = Promise.resolve({ id: "product_123" });

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-product-accent bg-clip-text text-transparent mb-4">
            Product Management Redesign
          </h1>
          <p className="text-xl text-muted-foreground">
            Enhanced UX for product creation and detail views
          </p>
        </div>

        <Tabs defaultValue="create" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="create" className="text-lg py-3">
              Create Product
            </TabsTrigger>
            <TabsTrigger value="detail" className="text-lg py-3">
              Product Detail
            </TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-6">
            <Card className="border-2 border-primary/20">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>New Product Creation</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Modern product creation form with enhanced UX, smart validation, 
                  drag-and-drop image uploads, and real-time price calculation.
                </p>
              </CardContent>
            </Card>
            <NewProductPage />
          </TabsContent>

          <TabsContent value="detail" className="space-y-6">
            <Card className="border-2 border-primary/20">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>Product Detail View</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Enhanced product detail page with better information architecture, 
                  AI visual search integration, and improved action accessibility.
                </p>
              </CardContent>
            </Card>
            <ProductDetailPage params={mockParams} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}