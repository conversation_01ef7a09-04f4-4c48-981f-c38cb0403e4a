"use client";

import { useState } from "react";
import { useAction } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Settings, 
  Cloud, 
  TestTube, 
  BarChart3, 
  CheckCircle, 
  XCircle, 
  Loader2,
  AlertTriangle
} from "lucide-react";

export default function SettingsPage() {
  const [testInput, setTestInput] = useState("https://example.com/test-image.jpg");
  const [testType, setTestType] = useState<"text" | "image">("image");
  const [isTestingConfig, setIsTestingConfig] = useState(false);
  const [isTestingEmbedding, setIsTestingEmbedding] = useState(false);
  const [isGettingStats, setIsGettingStats] = useState(false);
  const [configResult, setConfigResult] = useState<any>(null);
  const [testResult, setTestResult] = useState<any>(null);
  const [statsResult, setStatsResult] = useState<any>(null);

  const validateConfig = useAction(api.embeddings.validateGoogleCloudConfig);
  const testEmbedding = useAction(api.embeddings.testEmbeddingSystem);
  const getStats = useAction(api.embeddings.getEmbeddingStats);

  const handleValidateConfig = async () => {
    setIsTestingConfig(true);
    try {
      const result = await validateConfig({});
      setConfigResult(result);
    } catch (error) {
      setConfigResult({
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setIsTestingConfig(false);
    }
  };

  const handleTestEmbedding = async () => {
    if (!testInput.trim()) {
      alert(`Please enter a test ${testType === 'image' ? 'image URL' : 'text'}`);
      return;
    }

    setIsTestingEmbedding(true);
    try {
      const result = await testEmbedding({ input: testInput, type: testType });
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setIsTestingEmbedding(false);
    }
  };

  const handleGetStats = async () => {
    setIsGettingStats(true);
    try {
      const result = await getStats({});
      setStatsResult(result);
    } catch (error) {
      setStatsResult({
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setIsGettingStats(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
          <Settings className="h-8 w-8 mr-3" />
          Settings
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Manage system settings and integrations
        </p>
      </div>

      {/* Google Vertex AI Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Cloud className="h-5 w-5 mr-2" />
            Google Vertex AI Configuration
          </CardTitle>
          <CardDescription>
            Configure and test your Google Vertex AI integration for image embeddings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Configuration Status</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Validate your Google Cloud setup
              </p>
            </div>
            <Button 
              onClick={handleValidateConfig} 
              disabled={isTestingConfig}
              variant="outline"
            >
              {isTestingConfig ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <TestTube className="h-4 w-4 mr-2" />
              )}
              Test Configuration
            </Button>
          </div>

          {configResult && (
            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                {configResult.isValid ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
                <span className="font-medium">
                  {configResult.isValid ? "Configuration Valid" : "Configuration Invalid"}
                </span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Project ID:</span>
                  <Badge variant={configResult.projectId ? "default" : "destructive"}>
                    {configResult.projectId || "Not Set"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Location:</span>
                  <Badge variant={configResult.location ? "default" : "secondary"}>
                    {configResult.location || "us-central1 (default)"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Credentials:</span>
                  <Badge variant={configResult.hasCredentials ? "default" : "destructive"}>
                    {configResult.hasCredentials ? "Configured" : "Missing"}
                  </Badge>
                </div>
                {configResult.error && (
                  <div className="text-red-600 dark:text-red-400 mt-2">
                    <AlertTriangle className="h-4 w-4 inline mr-1" />
                    {configResult.error}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Embedding Generation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TestTube className="h-5 w-5 mr-2" />
            Test Embedding Generation
          </CardTitle>
          <CardDescription>
            Test the embedding generation with sample text or images
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4 mb-4">
            <Button
              variant={testType === "text" ? "default" : "outline"}
              onClick={() => {
                setTestType("text");
                setTestInput("A high-quality wireless bluetooth headphone with noise cancellation and premium sound quality.");
              }}
              className="flex-1"
            >
              Text Embedding
            </Button>
            <Button
              variant={testType === "image" ? "default" : "outline"}
              onClick={() => {
                setTestType("image");
                setTestInput("https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500");
              }}
              className="flex-1"
            >
              Image Embedding
            </Button>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              {testType === "image" ? "Test Image URL" : "Test Text"}
            </label>
            {testType === "image" ? (
              <div className="space-y-2">
                <Input
                  value={testInput}
                  onChange={(e) => setTestInput(e.target.value)}
                  placeholder="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Try: Unsplash images, product photos, or any publicly accessible image URL
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                <textarea
                  value={testInput}
                  onChange={(e) => setTestInput(e.target.value)}
                  placeholder="Enter product description, search query, or any text to generate embeddings for..."
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-white"
                  rows={3}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Try: Product descriptions, search queries, or any text content
                </p>
              </div>
            )}
          </div>

          <Button
            onClick={handleTestEmbedding}
            disabled={isTestingEmbedding || !testInput.trim()}
          >
            {isTestingEmbedding ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <TestTube className="h-4 w-4 mr-2" />
            )}
            Generate {testType === "image" ? "Image" : "Text"} Embedding
          </Button>

          {testResult && (
            <div className="p-4 border rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                {testResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
                <span className="font-medium">
                  {testResult.success ? "Embedding Generated Successfully" : "Embedding Generation Failed"}
                </span>
              </div>

              {testResult.success ? (
                <div className="space-y-3 text-sm">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex justify-between">
                      <span>Input Type:</span>
                      <Badge variant="outline">{testResult.embedding.inputType}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Dimensions:</span>
                      <Badge variant="default">{testResult.embedding.dimensions}</Badge>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium">Input Value:</span>
                    <div className="text-xs mt-1 p-2 bg-blue-50 dark:bg-blue-900/20 rounded border-l-2 border-blue-500">
                      {testResult.embedding.inputValue}
                    </div>
                  </div>

                  <div>
                    <span className="font-medium">Sample Embedding Values:</span>
                    <div className="font-mono text-xs mt-1 p-2 bg-gray-100 dark:bg-gray-800 rounded">
                      [{testResult.embedding.sampleValues.map((v: number) => v.toFixed(4)).join(', ')}...]
                    </div>
                  </div>

                  {testResult.embedding.dimensions > 0 && (
                    <div className="text-xs text-green-600 dark:text-green-400 flex items-center gap-1">
                      <CheckCircle className="w-3 h-3" />
                      Embedding generated successfully with {testResult.embedding.dimensions} dimensions
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-red-600 dark:text-red-400">
                  <AlertTriangle className="h-4 w-4 inline mr-1" />
                  {testResult.error}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Embedding Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Embedding Statistics
          </CardTitle>
          <CardDescription>
            View statistics about your product embeddings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={handleGetStats} 
            disabled={isGettingStats}
            variant="outline"
          >
            {isGettingStats ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <BarChart3 className="h-4 w-4 mr-2" />
            )}
            Get Statistics
          </Button>

          {statsResult && (
            <div className="p-4 border rounded-lg">
              {statsResult.error ? (
                <div className="text-red-600 dark:text-red-400">
                  <AlertTriangle className="h-4 w-4 inline mr-1" />
                  {statsResult.error}
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{statsResult.totalProducts}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Total Products</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{statsResult.productsWithEmbeddings}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">With Embeddings</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{statsResult.coveragePercentage.toFixed(1)}%</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Coverage</div>
                  </div>
                  {statsResult.dimensions > 0 && (
                    <>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{statsResult.dimensions}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">Dimensions</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{statsResult.avgMagnitude.toFixed(2)}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">Avg Magnitude</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold">{statsResult.count}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">Embeddings</div>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Setup Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Setup Instructions</CardTitle>
          <CardDescription>
            Follow these steps to configure Google Vertex AI
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-start space-x-2">
              <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-xs font-medium">1</div>
              <div>
                <div className="font-medium">Create Google Cloud Project</div>
                <div className="text-gray-600 dark:text-gray-400">Set up a new GCP project with billing enabled</div>
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-xs font-medium">2</div>
              <div>
                <div className="font-medium">Enable Vertex AI API</div>
                <div className="text-gray-600 dark:text-gray-400">Enable the Vertex AI API in your project</div>
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-xs font-medium">3</div>
              <div>
                <div className="font-medium">Create Service Account</div>
                <div className="text-gray-600 dark:text-gray-400">Create a service account with Vertex AI User role</div>
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-xs font-medium">4</div>
              <div>
                <div className="font-medium">Set Environment Variables</div>
                <div className="text-gray-600 dark:text-gray-400">Configure GOOGLE_APPLICATION_CREDENTIALS and GCP_PROJECT_ID</div>
              </div>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div className="text-sm">
              <div className="font-medium mb-1">📖 Detailed Setup Guide</div>
              <div className="text-gray-600 dark:text-gray-400">
                See <code>docs/GOOGLE_VERTEX_AI_SETUP.md</code> for complete setup instructions
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
