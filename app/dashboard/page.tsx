"use client";

import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Package,
  ShoppingCart,
  Users,
  TrendingUp,
  AlertCircle,
  Plus,
  Eye,
} from "lucide-react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from "recharts";

export default function DashboardPage() {
  // Get dashboard data
  const products = useQuery(api.products.getProducts, {});
  const orders = useQuery(api.orders.getOrders, {});

  // Calculate metrics
  const productsArray = Array.isArray(products) ? products : products?.page || [];
  const ordersArray = Array.isArray(orders) ? orders : orders?.page || [];

  const totalProducts = productsArray.length;
  const totalOrders = ordersArray.length;
  const actionRequiredOrders = ordersArray.filter((order: any) => order.status === "action_required").length;
  const newOrders = ordersArray.filter((order: any) => order.status === "new").length;

  const metrics = [
    {
      title: "Total Products",
      value: totalProducts,
      description: "Active products in catalog",
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Total Orders",
      value: totalOrders,
      description: "All time orders",
      icon: ShoppingCart,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "New Orders",
      value: newOrders,
      description: "Awaiting processing",
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Action Required",
      value: actionRequiredOrders,
      description: "Orders needing attention",
      icon: AlertCircle,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
  ];

  const quickActions = [
    {
      title: "Add Product",
      description: "Add a new product to catalog",
      href: "/dashboard/products/new",
      icon: Plus,
      color: "bg-blue-600 hover:bg-blue-700",
    },
    {
      title: "View Orders",
      description: "Manage customer orders",
      href: "/dashboard/orders",
      icon: Eye,
      color: "bg-green-600 hover:bg-green-700",
    },
  ];

  // Sample data for charts (in a real app, this would come from your database)
  const salesData = [
    { name: "Jan", sales: 4000, orders: 24 },
    { name: "Feb", sales: 3000, orders: 18 },
    { name: "Mar", sales: 5000, orders: 32 },
    { name: "Apr", sales: 4500, orders: 28 },
    { name: "May", sales: 6000, orders: 38 },
    { name: "Jun", sales: 5500, orders: 35 },
  ];

  const orderStatusData = [
    { name: "New", value: newOrders },
    { name: "Processing", value: Math.floor(totalOrders * 0.4) },
    { name: "Shipped", value: Math.floor(totalOrders * 0.3) },
    { name: "Delivered", value: Math.floor(totalOrders * 0.2) },
    { name: "Action Required", value: actionRequiredOrders },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Welcome to your MaoMao admin dashboard. Here's what's happening with your business.
        </p>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric) => {
          const Icon = metric.icon;
          return (
            <Card key={metric.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {metric.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${metric.bgColor}`}>
                  <Icon className={`h-4 w-4 ${metric.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {metric.value}
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  {metric.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks to manage your business
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {quickActions.map((action) => {
              const Icon = action.icon;
              return (
                <Button
                  key={action.title}
                  variant="outline"
                  className="h-auto p-4 justify-start"
                  asChild
                >
                  <a href={action.href}>
                    <div className={`p-2 rounded-lg ${action.color} mr-4`}>
                      <Icon className="h-4 w-4 text-white" />
                    </div>
                    <div className="text-left">
                      <div className="font-medium">{action.title}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {action.description}
                      </div>
                    </div>
                  </a>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Sales Trend</CardTitle>
            <CardDescription>Monthly sales and order volume</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={salesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="sales"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  name="Sales ($)"
                />
                <Line
                  type="monotone"
                  dataKey="orders"
                  stroke="#10b981"
                  strokeWidth={2}
                  name="Orders"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Order Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Order Status</CardTitle>
            <CardDescription>Current order distribution by status</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={orderStatusData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
            <CardDescription>Latest customer orders</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {ordersArray.slice(0, 5).map((order: any) => (
                <div key={order._id} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-sm">Order #{order._id.slice(-6)}</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {order.items.length} items • ${order.totalAmount}
                    </p>
                  </div>
                  <Badge
                    variant={
                      order.status === "new" ? "default" :
                      order.status === "action_required" ? "destructive" :
                      order.status === "shipped" ? "secondary" :
                      "outline"
                    }
                  >
                    {order.status.replace("_", " ")}
                  </Badge>
                </div>
              )) || (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  No orders yet
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Low Stock Products */}
        <Card>
          <CardHeader>
            <CardTitle>Low Stock Alert</CardTitle>
            <CardDescription>Products running low on inventory</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {productsArray.filter((product: any) => product.stockCount < 10).slice(0, 5).map((product: any) => (
                <div key={product._id} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-sm">{product.title}</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Stock: {product.stockCount} units
                    </p>
                  </div>
                  <Badge variant="destructive">
                    Low Stock
                  </Badge>
                </div>
              )) || (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  All products well stocked
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
