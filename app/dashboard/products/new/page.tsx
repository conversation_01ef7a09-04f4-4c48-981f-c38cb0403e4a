"use client";

import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { PageHeader } from "@/components/products/PageHeader";
import { ProductInfoForm } from "@/components/products/ProductInfoForm";
import { PriceCalculator } from "@/components/products/PriceCalculator";
import { TagManager } from "@/components/products/TagManager";
import { ImageUploadArea } from "@/components/products/ImageUploadArea";
import { ProductSpecificationsForm } from "@/components/products/ProductSpecificationsForm";
import { PricingTiersForm } from "@/components/products/PricingTiersForm";
import { CustomizationOptionsForm } from "@/components/products/CustomizationOptionsForm";
import { ProductVariantsForm } from "@/components/products/ProductVariantsForm";
import { ProductStatus, ImageUploadState, mockRootProps } from "@/app/productRedesignMockData";
import { Loader2 } from "lucide-react";
import Link from "next/link";

interface ImageFile {
  file: File;
  preview: string;
  uploaded: boolean;
  storageId?: string;
  uploadState: ImageUploadState;
}

interface FormData {
  title: string;
  description: string;
  curationNotes: string;
  supplierId: string;
  priceInYuan: number;
  serviceFee: number;
  stockCount: number;
  status: ProductStatus;
  tags: string[];
  images: string[];
  categories: string[];
  specifications: {
    weight?: {
      value: number;
      unit: string;
    };
    dimensions?: {
      length?: number;
      width?: number;
      height?: number;
      unit: string;
    };
    material?: string;
    color?: string;
    customAttributes: Record<string, string>;
  };
  pricingTiers: Array<{
    minQuantity: number;
    pricePerUnit: number;
    currency: string;
    description?: string;
  }>;
  customizationOptions: Array<{
    name: string;
    type: string;
    additionalCost?: number;
    minQuantity?: number;
    description?: string;
  }>;
  variants: Array<{
    variantName: string;
    variantType: string;
    priceInYuan: number;
    serviceFee: number;
    finalPrice: number;
    stockCount: number;
    availableQuantity?: number;
    minQuantity?: number;
    status: "active" | "inactive" | "out_of_stock";
    isDefault: boolean;
    specifications?: {
      weight?: {
        value: number;
        unit: string;
      };
      customAttributes?: Record<string, string>;
    };
    pricingTiers?: Array<{
      minQuantity: number;
      pricePerUnit: number;
      currency: string;
      description?: string;
    }>;
  }>;
}

export default function NewProductPage() {
  const router = useRouter();
  
  // Mock data from the redesign scope
  const suppliers = mockRootProps.suppliers;
  
  const [formData, setFormData] = useState<FormData>({
    title: "",
    description: "",
    curationNotes: "",
    supplierId: "",
    priceInYuan: 0,
    serviceFee: 0,
    stockCount: 0,
    status: ProductStatus.ACTIVE,
    tags: [],
    images: [],
    categories: [],
    specifications: {
      customAttributes: {},
    },
    pricingTiers: [],
    customizationOptions: [],
    variants: [],
  });

  const [imageFiles, setImageFiles] = useState<ImageFile[]>([]);
  const [uploadingImages, setUploadingImages] = useState<Set<number>>(new Set());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const tagSuggestions = [
    "electronics", "premium", "wireless", "bluetooth", "audio", "gaming", 
    "home", "kitchen", "outdoor", "fitness", "tech", "gadgets", "accessories"
  ];

  const handleFieldChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = "Product title is required";
    }

    if (!formData.description.trim()) {
      newErrors.description = "Product description is required";
    }

    if (!formData.supplierId) {
      newErrors.supplierId = "Please select a supplier";
    }

    if (formData.priceInYuan <= 0) {
      newErrors.priceInYuan = "Price must be greater than 0";
    }

    // Check if there are unuploaded images
    const unuploadedImages = imageFiles.filter(img => !img.uploaded);
    if (unuploadedImages.length > 0) {
      newErrors.images = "Please wait for all images to finish uploading";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const uploadImage = async (index: number): Promise<void> => {
    const imageFile = imageFiles[index];
    if (!imageFile || imageFile.uploaded) return;

    setUploadingImages(prev => new Set(prev).add(index));

    try {
      // Mock upload process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const storageId = `storage_${Date.now()}_${index}`;
      
      // Update the image file with storage ID
      setImageFiles(prev => prev.map((img, i) =>
        i === index ? { ...img, uploaded: true, storageId, uploadState: ImageUploadState.UPLOADED } : img
      ));

      // Add to form data images array
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, storageId]
      }));

    } catch (error) {
      console.error("Error uploading image:", error);
      // Update upload state to error
      setImageFiles(prev => prev.map((img, i) =>
        i === index ? { ...img, uploadState: ImageUploadState.ERROR } : img
      ));
    } finally {
      setUploadingImages(prev => {
        const newSet = new Set(prev);
        newSet.delete(index);
        return newSet;
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log("Creating product:", formData);
      
      // Clean up preview URLs
      imageFiles.forEach(img => URL.revokeObjectURL(img.preview));

      // Redirect to products list
      router.push("/dashboard/products");
    } catch (error) {
      console.error("Error creating product:", error);
      setErrors({ submit: "Failed to create product. Please try again." });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8 space-y-8">
        <PageHeader
          title="Add New Product"
          description="Create a new product for your catalog with enhanced AI-powered features"
          backLink="/dashboard/products"
          backLabel="Back to Products"
        />

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              <ProductInfoForm
                title={formData.title}
                description={formData.description}
                curationNotes={formData.curationNotes}
                supplierId={formData.supplierId}
                status={formData.status}
                stockCount={formData.stockCount}
                suppliers={suppliers}
                categories={formData.categories}
                onFieldChange={handleFieldChange}
                errors={errors}
              />

              <PriceCalculator
                priceInYuan={formData.priceInYuan}
                serviceFee={formData.serviceFee}
                onPriceChange={handleFieldChange}
              />

              <TagManager
                tags={formData.tags}
                onTagsChange={(tags) => handleFieldChange("tags", tags)}
                suggestions={tagSuggestions}
              />

              <ProductSpecificationsForm
                specifications={formData.specifications}
                onSpecificationsChange={(specs) => handleFieldChange("specifications", specs)}
                errors={errors}
              />

              <PricingTiersForm
                pricingTiers={formData.pricingTiers}
                onPricingTiersChange={(tiers) => handleFieldChange("pricingTiers", tiers)}
                errors={errors}
              />

              <CustomizationOptionsForm
                customizationOptions={formData.customizationOptions}
                onCustomizationOptionsChange={(options) => handleFieldChange("customizationOptions", options)}
                errors={errors}
              />

              <ProductVariantsForm
                variants={formData.variants}
                onVariantsChange={(variants) => handleFieldChange("variants", variants)}
                basePrice={formData.priceInYuan}
                errors={errors}
              />
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              <ImageUploadArea
                imageFiles={imageFiles}
                onImageFilesChange={setImageFiles}
                onUploadImage={uploadImage}
                uploadingImages={uploadingImages}
                maxImages={10}
              />
            </div>
          </div>

          {/* Submit Actions */}
          <div className="flex justify-end space-x-4 pt-8 border-t">
            <Button type="button" variant="outline" asChild>
              <Link href="/dashboard/products">Cancel</Link>
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="min-w-32"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Product"
              )}
            </Button>
          </div>

          {errors.submit && (
            <div className="text-center">
              <p className="text-sm text-destructive">{errors.submit}</p>
            </div>
          )}
        </form>
      </div>
    </div>
  );
}