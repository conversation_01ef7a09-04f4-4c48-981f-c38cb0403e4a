"use client";

import React, { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PageHeader } from "@/components/products/PageHeader";
import { ProductImageGallery } from "@/components/products/ProductImageGallery";
import { SupplierInfoCard } from "@/components/products/SupplierInfoCard";
import { StatusBadge, StockBadge } from "@/components/products/StatusBadge";
import { mockRootProps, formatPrice, formatYuanPrice, formatDate } from "@/app/productRedesignMockData";
import { Edit, Package, DollarSign, Tag, CheckCircle, CircleAlert } from "lucide-react";
import Link from "next/link";

interface ProductDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
  const [productId, setProductId] = React.useState<string | null>(null);
  const [isGeneratingEmbedding, setIsGeneratingEmbedding] = useState(false);

  // Handle async params in Next.js 15
  React.useEffect(() => {
    params.then(p => setProductId(p.id));
  }, [params]);

  // Mock data - in real app this would come from API
  const product = mockRootProps.existingProduct;
  const supplier = mockRootProps.suppliers.find(s => s._id === product.supplierId);

  const handleGenerateEmbedding = async () => {
    setIsGeneratingEmbedding(true);
    try {
      // Mock embedding generation
      await new Promise(resolve => setTimeout(resolve, 3000));
      console.log("Generated embedding for product:", productId);
    } catch (error) {
      console.error("Error generating embedding:", error);
    } finally {
      setIsGeneratingEmbedding(false);
    }
  };

  if (!product) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading product...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8 space-y-8">
        <PageHeader
          title={product.title}
          backLink="/dashboard/products"
          backLabel="Back to Products"
          badges={[
            { 
              label: product.status === "active" ? "Active" : product.status === "inactive" ? "Inactive" : "Archived",
              variant: product.status === "active" ? "default" : product.status === "inactive" ? "secondary" : "outline"
            },
            {
              label: product.stockCount === 0 ? "Out of Stock" : 
                     product.stockCount < 10 ? "Low Stock" : 
                     product.stockCount < 50 ? "Medium Stock" : "In Stock",
              variant: product.stockCount === 0 ? "destructive" : 
                      product.stockCount < 10 ? "destructive" : 
                      product.stockCount < 50 ? "secondary" : "default"
            }
          ]}
          actions={
            <Button asChild>
              <Link href={`/dashboard/products/${product._id}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Product
              </Link>
            </Button>
          }
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Product Images & AI Analysis */}
            <ProductImageGallery
              images={product.images}
              title={product.title}
              hasEmbedding={!!product.imageEmbedding}
              embeddingDimensions={product.imageEmbedding?.length}
              onGenerateEmbedding={handleGenerateEmbedding}
              isGeneratingEmbedding={isGeneratingEmbedding}
            />

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                  {product.description}
                </p>
              </CardContent>
            </Card>

            {/* Curation Notes */}
            {product.curationNotes && (
              <Card className="border-l-4 border-l-primary bg-primary/5">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <span>Curation Notes</span>
                    <Badge variant="outline" className="bg-primary/10 text-primary">
                      Expert Pick
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-foreground whitespace-pre-wrap leading-relaxed font-medium">
                    {product.curationNotes}
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Tags */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Tag className="h-5 w-5" />
                  <span>Product Tags</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {product.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="px-3 py-1">
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Search & Discovery Insights */}
            <Card>
              <CardHeader>
                <CardTitle>Search & Discovery</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h4 className="font-semibold flex items-center space-x-2">
                      <span>Text Search</span>
                      <CheckCircle className="h-4 w-4 text-success" />
                    </h4>
                    <div className="text-sm text-muted-foreground space-y-2">
                      <div className="flex items-start space-x-2">
                        <span className="font-medium min-w-16">Title:</span>
                        <span>"{product.title}"</span>
                      </div>
                      <div className="flex items-start space-x-2">
                        <span className="font-medium min-w-16">Tags:</span>
                        <span>{product.tags.join(", ")}</span>
                      </div>
                      <div className="flex items-start space-x-2">
                        <span className="font-medium min-w-16">Content:</span>
                        <span>Description keywords available</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <h4 className="font-semibold flex items-center space-x-2">
                      <span>Visual Search</span>
                      {product.imageEmbedding ? (
                        <CheckCircle className="h-4 w-4 text-success" />
                      ) : (
                        <CircleAlert className="h-4 w-4 text-warning" />
                      )}
                    </h4>
                    <div className="text-sm text-muted-foreground space-y-2">
                      {product.imageEmbedding ? (
                        <>
                          <div className="flex items-center space-x-2 text-success">
                            <CheckCircle className="h-3 w-3" />
                            <span>Available for image search</span>
                          </div>
                          <div>• Customers can upload similar images</div>
                          <div>• AI will find visual matches</div>
                        </>
                      ) : (
                        <>
                          <div className="flex items-center space-x-2 text-warning">
                            <CircleAlert className="h-3 w-3" />
                            <span>Not available for image search</span>
                          </div>
                          <div>• Upload images to enable</div>
                          <div>• Generate embeddings required</div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Pricing */}
            <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-product-accent/5">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5 text-primary" />
                  <span>Pricing</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Price in Yuan:
                    </span>
                    <span className="font-semibold">{formatYuanPrice(product.priceInYuan)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Service Fee:
                    </span>
                    <span className="font-semibold">{formatPrice(product.serviceFee)}</span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between items-center">
                      <span className="font-semibold">Final Price:</span>
                      <span className="font-bold text-2xl text-primary">
                        {formatPrice(product.finalPrice)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Inventory */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Package className="h-5 w-5" />
                  <span>Inventory</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center space-y-4">
                  <div className="text-4xl font-bold text-foreground">
                    {product.stockCount}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    units in stock
                  </div>
                  <StockBadge stockCount={product.stockCount} />
                </div>
              </CardContent>
            </Card>

            {/* Supplier */}
            {supplier && <SupplierInfoCard supplier={supplier} />}

            {/* Product Metadata */}
            <Card>
              <CardHeader>
                <CardTitle>Product Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Product ID:</span>
                  <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                    {product._id}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Created:</span>
                  <span>{formatDate(product._creationTime)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Status:</span>
                  <StatusBadge status={product.status as any} />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}