"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { SupplierForm } from "@/components/suppliers/SupplierForm";
import { toast } from "sonner";

export default function NewSupplierPage() {
  const router = useRouter();
  const createSupplier = useMutation(api.suppliers.createSupplier);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (formData: any) => {
    setIsSubmitting(true);
    setErrors({});

    try {
      await createSupplier(formData);
      toast.success("Supplier created successfully!");
      router.push("/dashboard/suppliers");
    } catch (error) {
      console.error("Error creating supplier:", error);
      toast.error("Failed to create supplier. Please try again.");

      if (error instanceof Error) {
        if (error.message.includes("name")) {
          setErrors({ name: error.message });
        } else {
          setErrors({ submit: error.message });
        }
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push("/dashboard/suppliers");
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/dashboard/suppliers">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Suppliers
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Add New Supplier</h1>
          <p className="text-muted-foreground">
            Create a new supplier profile with contact and business information
          </p>
        </div>
      </div>

      {/* Supplier Form */}
      <SupplierForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isSubmitting={isSubmitting}
        errors={errors}
      />
    </div>
  );
}
