"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { GroupBuyForm } from "@/components/group-buys/GroupBuyForm";
import { toast } from "sonner";

export default function NewGroupBuyPage() {
  const router = useRouter();
  const products = useQuery(api.products.getProducts, {}) || { page: [] };
  const createGroupBuy = useMutation(api.groupBuyUtils.create);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = async (formData: any) => {
    setIsSubmitting(true);
    setErrors({});

    try {
      await createGroupBuy(formData);
      toast.success("Group buy created successfully!");
      router.push("/dashboard/group-buys");
    } catch (error) {
      console.error("Error creating group buy:", error);
      toast.error("Failed to create group buy. Please try again.");

      if (error instanceof Error) {
        if (error.message.includes("product")) {
          setErrors({ productId: error.message });
        } else if (error.message.includes("title")) {
          setErrors({ title: error.message });
        } else {
          setErrors({ submit: error.message });
        }
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push("/dashboard/group-buys");
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/dashboard/group-buys">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Group Buys
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create Group Buy</h1>
          <p className="text-muted-foreground">
            Start a new group buying campaign with quantity-based pricing
          </p>
        </div>
      </div>

      {/* Group Buy Form */}
      <GroupBuyForm
        products={products.page}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        isSubmitting={isSubmitting}
        errors={errors}
      />
    </div>
  );
}
