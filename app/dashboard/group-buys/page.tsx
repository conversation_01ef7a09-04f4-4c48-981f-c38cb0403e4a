"use client";

import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  Search, 
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Plus,
  Users,
  Clock,
  Target,

} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";

export default function GroupBuysPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Fetch group buys
  const groupBuys = useQuery(api.groupBuys.getGroupBuys, {
    status: statusFilter === "all" ? undefined : statusFilter as any
  });

  // Filter group buys based on search
  const filteredGroupBuys = (groupBuys || []).filter((groupBuy: any) =>
    groupBuy.product?.title?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate stats
  const totalGroupBuys = groupBuys?.length || 0;
  const activeGroupBuys = groupBuys?.filter((gb: any) => gb.status === "active").length || 0;
  const completedGroupBuys = groupBuys?.filter((gb: any) => gb.status === "completed").length || 0;
  const totalParticipants = groupBuys?.reduce((sum: number, gb: any) => sum + gb.currentParticipants, 0) || 0;

  // Status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const config = {
      active: { label: "Active", className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" },
      completed: { label: "Completed", className: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" },
      expired: { label: "Expired", className: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200" },
    }[status] || { label: status, className: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200" };

    return (
      <Badge className={config.className}>
        {config.label}
      </Badge>
    );
  };

  // Time remaining component
  const TimeRemaining = ({ endTime }: { endTime: number }) => {
    const now = Date.now();
    const remaining = endTime - now;
    
    if (remaining <= 0) {
      return <span className="text-red-500">Expired</span>;
    }

    const days = Math.floor(remaining / (1000 * 60 * 60 * 24));
    const hours = Math.floor((remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) {
      return <span className="text-sm">{days}d {hours}h</span>;
    } else if (hours > 0) {
      return <span className="text-sm">{hours}h</span>;
    } else {
      const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
      return <span className="text-sm text-orange-500">{minutes}m</span>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Group Buys</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Manage group buying campaigns and track participation
          </p>
        </div>
        <Link href="/dashboard/group-buys/new">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Create Group Buy
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Group Buys</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalGroupBuys}</div>
            <p className="text-xs text-muted-foreground">
              All campaigns
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeGroupBuys}</div>
            <p className="text-xs text-muted-foreground">
              Currently running
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedGroupBuys}</div>
            <p className="text-xs text-muted-foreground">
              Successfully completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Participants</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalParticipants}</div>
            <p className="text-xs text-muted-foreground">
              Across all campaigns
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Group Buys Table */}
      <Card>
        <CardHeader>
          <CardTitle>Group Buy Campaigns</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search group buys by product name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Campaigns</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Progress</TableHead>
                  <TableHead>Participants</TableHead>
                  <TableHead>Current Price</TableHead>
                  <TableHead>Time Left</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredGroupBuys.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      {groupBuys?.length === 0 ? (
                        <div>
                          <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-500 dark:text-gray-400 mb-2">
                            No group buys found
                          </p>
                          <Link href="/dashboard/group-buys/new">
                            <Button>
                              <Plus className="w-4 h-4 mr-2" />
                              Create First Group Buy
                            </Button>
                          </Link>
                        </div>
                      ) : (
                        <p className="text-gray-500 dark:text-gray-400">
                          No group buys match your search criteria
                        </p>
                      )}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredGroupBuys.map((groupBuy: any) => {
                    const maxTier = groupBuy.targetTiers?.sort((a: any, b: any) => b.quantity - a.quantity)[0];
                    const progress = maxTier ? Math.min(100, (groupBuy.currentParticipants / maxTier.quantity) * 100) : 0;
                    const currentTier = groupBuy.targetTiers
                      ?.filter((tier: any) => groupBuy.currentParticipants >= tier.quantity)
                      ?.sort((a: any, b: any) => b.quantity - a.quantity)[0];

                    return (
                      <TableRow key={groupBuy._id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {groupBuy.product?.title || "Unknown Product"}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              Created {new Date(groupBuy._creationTime).toLocaleDateString()}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-2">
                            <Progress value={progress} className="w-[100px]" />
                            <div className="text-xs text-gray-500">
                              {progress.toFixed(0)}% complete
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Users className="w-4 h-4 text-gray-400" />
                            <span className="font-medium">{groupBuy.currentParticipants}</span>
                            {maxTier && (
                              <span className="text-gray-500">/ {maxTier.quantity}</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">
                            ${currentTier ? currentTier.price.toFixed(2) : "N/A"}
                          </div>
                          {groupBuy.product?.finalPrice && currentTier && (
                            <div className="text-xs text-green-600">
                              Save ${(groupBuy.product.finalPrice - currentTier.price).toFixed(2)}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4 text-gray-400" />
                            <TimeRemaining endTime={groupBuy.endTime} />
                          </div>
                        </TableCell>
                        <TableCell>
                          <StatusBadge status={groupBuy.status} />
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={`/dashboard/group-buys/${groupBuy._id}`}>
                                  <Eye className="w-4 h-4 mr-2" />
                                  View Details
                                </Link>
                              </DropdownMenuItem>
                              <DropdownMenuItem asChild>
                                <Link href={`/dashboard/group-buys/${groupBuy._id}/edit`}>
                                  <Edit className="w-4 h-4 mr-2" />
                                  Edit Campaign
                                </Link>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>

          {/* Loading state */}
          <div className="text-center py-4 text-sm text-gray-500 dark:text-gray-400">
            {groupBuys ? "All group buys loaded" : "Loading group buys..."}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
