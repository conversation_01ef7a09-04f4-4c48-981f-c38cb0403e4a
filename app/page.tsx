"use client";

import Link from "next/link";

export default function Home() {
  return (
    <>
      <header className="sticky top-0 z-10 bg-background p-4 border-b-2 border-slate-200 dark:border-slate-800 flex flex-row justify-between items-center">
        Welcome to our App!
        <Link href="/signin" className="bg-slate-200 dark:bg-slate-800 text-foreground rounded-md px-2 py-1">
          Sign In
        </Link>
      </header>
      <main className="p-8 flex flex-col gap-8 items-center justify-center min-h-[calc(100vh-64px)]">
        <h1 className="text-5xl font-bold text-center">
          Discover Amazing Features
        </h1>
        <p className="text-lg text-center max-w-2xl">
          Explore what our application has to offer. Sign in to get started or learn more about our platform.
        </p>
        <div className="flex gap-4 mt-4">
          <Link href="/signin" className="bg-foreground text-background text-lg px-6 py-3 rounded-md shadow-lg hover:bg-opacity-90 transition-colors">
            Get Started
          </Link>
          <Link href="/about" className="bg-slate-200 dark:bg-slate-800 text-foreground text-lg px-6 py-3 rounded-md shadow-lg hover:bg-opacity-90 transition-colors">
            Learn More
          </Link>
        </div>
      </main>
    </>
  );
}
