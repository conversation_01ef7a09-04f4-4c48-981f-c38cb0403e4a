// Product status options
export enum ProductStatus {
  ACTIVE = "active",
  INACTIVE = "inactive", 
  ARCHIVED = "archived"
}

// Stock level indicators
export enum StockLevel {
  OUT_OF_STOCK = "out_of_stock",
  LOW_STOCK = "low_stock",
  MEDIUM_STOCK = "medium_stock", 
  IN_STOCK = "in_stock"
}

// Image upload states
export enum ImageUploadState {
  PENDING = "pending",
  UPLOADING = "uploading",
  UPLOADED = "uploaded",
  ERROR = "error"
}

// AI embedding status
export enum EmbeddingStatus {
  NOT_GENERATED = "not_generated",
  GENERATING = "generating",
  GENERATED = "generated",
  ERROR = "error"
}

export const formatPrice = (price: number): string => {
  return `$${price.toFixed(2)}`;
};

export const formatYuanPrice = (price: number): string => {
  return `¥${price.toFixed(2)}`;
};

export const formatStockCount = (count: number): string => {
  if (count === 0) return "Out of stock";
  if (count === 1) return "1 unit";
  return `${count} units`;
};

export const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const formatEmbeddingDimensions = (embedding: number[]): string => {
  return `${embedding.length} dimensions`;
};

// Mock data for new product creation
export const mockRootProps = {
  suppliers: [
    {
      _id: "supplier_1" as const,
      name: "Shanghai Electronics Co.",
      contactInfo: {
        email: "<EMAIL>",
        phone: "+86 21 1234 5678"
      },
      platformUrl: "https://alibaba.com/supplier/shanghai-electronics",
      isActive: true
    },
    {
      _id: "supplier_2" as const,
      name: "Guangzhou Manufacturing Ltd.",
      contactInfo: {
        email: "<EMAIL>", 
        phone: "+86 20 9876 5432"
      },
      platformUrl: "https://alibaba.com/supplier/guangzhou-mfg",
      isActive: true
    },
    {
      _id: "supplier_3" as const,
      name: "Shenzhen Tech Solutions",
      contactInfo: {
        email: "<EMAIL>",
        phone: "+86 755 1111 2222"
      },
      platformUrl: "https://alibaba.com/supplier/shenzhen-tech",
      isActive: true
    }
  ],
  existingProduct: {
    _id: "product_123" as const,
    _creationTime: 1704067200000,
    title: "Wireless Bluetooth Earbuds Pro",
    description: "Premium wireless earbuds with active noise cancellation, 30-hour battery life, and crystal-clear audio quality. Perfect for music lovers and professionals who demand the best audio experience.",
    curationNotes: "These earbuds stand out for their exceptional build quality and superior sound engineering. The active noise cancellation rivals premium brands at a fraction of the cost, making them an incredible value proposition for our customers.",
    supplierId: "supplier_1" as const,
    priceInYuan: 180.50,
    serviceFee: 12.00,
    finalPrice: 39.08,
    stockCount: 150,
    status: ProductStatus.ACTIVE,
    tags: ["electronics", "audio", "wireless", "bluetooth", "premium"],
    images: [
      "https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=400",
      "https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=400",
      "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400"
    ],
    imageEmbedding: new Array(512).fill(0).map(() => Math.random() * 2 - 1)
  },
  imageFiles: [
    {
      file: new File([], "earbuds-main.jpg"),
      preview: "https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=400",
      uploaded: true,
      storageId: "storage_id_1" as const,
      uploadState: ImageUploadState.UPLOADED
    },
    {
      file: new File([], "earbuds-case.jpg"), 
      preview: "https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=400",
      uploaded: true,
      storageId: "storage_id_2" as const,
      uploadState: ImageUploadState.UPLOADED
    },
    {
      file: new File([], "earbuds-lifestyle.jpg"),
      preview: "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400", 
      uploaded: false,
      uploadState: ImageUploadState.PENDING
    }
  ]
};