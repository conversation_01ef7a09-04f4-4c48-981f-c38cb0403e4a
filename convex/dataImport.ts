import { v } from "convex/values";
import { action, internalMutation, internalQuery } from "./_generated/server";
import { api } from "./_generated/api";
import { auth } from "./auth";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";

// Types for Alibaba data transformation
type AlibabaProduct = {
  name: string;
  productUrl: string;
  attributes: Record<string, any>;
  weight?: number | Record<string, number> | null;
  weightUnit?: string;
  categories?: string[];
  offers?: any[] | { pricing_tiers: any[] };
  offerList?: any[];
  customServices?: any[];
  productImagePreviews?: string[];
  productImageDescriptions?: string[];
  variants?: any[];
  processingStatus?: string;
  created?: string;
  updated?: string;
};

type DataQuality = "high" | "medium" | "low" | "needs_review";

// Data transformation utilities
function normalizeCurrency(currency: string): string {
  const currencyMap: Record<string, string> = {
    "¥": "CNY",
    "￥": "CNY",
    "元": "CNY",
    "$": "USD",
    "€": "EUR",
    "CNY": "CNY",
    "USD": "USD",
  };
  return currencyMap[currency] || "CNY";
}

function extractWeight(weight: any, weightUnit?: string) {
  if (!weight) return undefined;
  
  if (typeof weight === "number") {
    return {
      value: weight,
      unit: normalizeWeightUnit(weightUnit || "g")
    };
  }
  
  if (typeof weight === "object") {
    // Handle weight objects with multiple variants
    const firstKey = Object.keys(weight)[0];
    if (firstKey && typeof weight[firstKey] === "number") {
      return {
        value: weight[firstKey],
        unit: normalizeWeightUnit(weightUnit || "g")
      };
    }
  }
  
  return undefined;
}

function normalizeWeightUnit(unit: string): string {
  const unitMap: Record<string, string> = {
    "g": "g",
    "克": "g",
    "kg": "kg",
    "公斤": "kg",
    "千克": "kg",
    "KG": "kg",
    "Kg": "kg",
    "t": "t",
    "吨": "t",
  };
  return unitMap[unit] || "g";
}

function extractDimensions(attributes: Record<string, any>) {
  const dimensions: any = {};
  
  // Look for dimension attributes in various formats
  const lengthKeys = ["长", "length", "长度", "长(cm)"];
  const widthKeys = ["宽", "width", "宽度", "宽(cm)"];
  const heightKeys = ["高", "height", "高度", "高(cm)"];
  
  for (const key of lengthKeys) {
    if (attributes[key] && typeof attributes[key] === "number") {
      dimensions.length = attributes[key];
      break;
    }
  }
  
  for (const key of widthKeys) {
    if (attributes[key] && typeof attributes[key] === "number") {
      dimensions.width = attributes[key];
      break;
    }
  }
  
  for (const key of heightKeys) {
    if (attributes[key] && typeof attributes[key] === "number") {
      dimensions.height = attributes[key];
      break;
    }
  }
  
  if (Object.keys(dimensions).length > 0) {
    return {
      ...dimensions,
      unit: "cm" // Default unit
    };
  }
  
  return undefined;
}

function transformOffers(offers: any): any[] {
  if (!offers) return [];
  
  let tierData: any[] = [];
  
  if (Array.isArray(offers)) {
    tierData = offers;
  } else if (offers.pricing_tiers && Array.isArray(offers.pricing_tiers)) {
    tierData = offers.pricing_tiers;
  }
  
  return tierData
    .map(tier => {
      const minQuantity = typeof tier.minQuantity === "string" 
        ? parseInt(tier.minQuantity) 
        : tier.minQuantity;
      const price = typeof tier.price === "string" 
        ? parseFloat(tier.price) 
        : tier.price;
        
      if (isNaN(minQuantity) || isNaN(price) || price <= 0) {
        return null;
      }
      
      return {
        minQuantity,
        pricePerUnit: price,
        currency: normalizeCurrency(tier.currency || "CNY"),
        description: tier.quantityInfo || undefined
      };
    })
    .filter(tier => tier !== null)
    .sort((a, b) => a.minQuantity - b.minQuantity);
}

function transformCustomServices(services: any[]): any[] {
  if (!services || !Array.isArray(services)) return [];
  
  return services
    .map(service => {
      if (!service.serviceName) return null;
      
      const additionalCost = service.price && typeof service.price === "number" 
        ? service.price 
        : undefined;
      const minQuantity = service.minQuantity && typeof service.minQuantity === "number"
        ? service.minQuantity
        : undefined;
        
      return {
        name: service.serviceName,
        type: service.serviceType || "custom",
        additionalCost,
        minQuantity,
        description: service.variantName || undefined
      };
    })
    .filter(service => service !== null);
}

function validateAndProcessImages(images: string[]): string[] {
  if (!images || !Array.isArray(images)) return [];
  
  return images
    .filter(url => {
      if (!url || typeof url !== "string") return false;
      // Basic URL validation
      try {
        new URL(url);
        return url.startsWith("http://") || url.startsWith("https://");
      } catch {
        return false;
      }
    })
    .slice(0, 10); // Limit to 10 images max
}

function assessDataQuality(product: AlibabaProduct): DataQuality {
  let score = 0;
  
  // Essential fields scoring
  if (product.name && product.name.length > 10) score += 20;
  if (product.productImagePreviews && product.productImagePreviews.length > 0) score += 30;
  if (product.offers || product.offerList) score += 25;
  if (product.attributes && Object.keys(product.attributes).length > 3) score += 15;
  if (product.weight && product.weightUnit) score += 10;
  
  if (score >= 80) return "high";
  if (score >= 60) return "medium";
  if (score >= 40) return "low";
  return "needs_review";
}

function filterRelevantAttributes(attributes: Record<string, any>): Record<string, any> {
  const filtered: Record<string, any> = {};
  const excludeKeys = ["长", "宽", "高", "重量", "length", "width", "height", "weight"];
  
  for (const [key, value] of Object.entries(attributes)) {
    if (!excludeKeys.includes(key) && value !== null && value !== undefined && value !== "") {
      // Convert arrays to strings for storage
      if (Array.isArray(value)) {
        filtered[key] = value.join(", ");
      } else {
        filtered[key] = value;
      }
    }
  }
  
  return filtered;
}

function deriveCategoriesFromName(name: string): string[] {
  // Simple category derivation based on keywords
  const categories: string[] = [];
  const nameLower = name.toLowerCase();
  
  const categoryKeywords = {
    "clothing": ["shirt", "dress", "pants", "jacket", "coat", "衣", "服装"],
    "electronics": ["phone", "computer", "tablet", "电子", "手机", "电脑"],
    "home": ["furniture", "decoration", "kitchen", "家具", "装饰", "厨房"],
    "beauty": ["cosmetic", "skincare", "makeup", "化妆", "护肤", "美容"],
    "jewelry": ["ring", "necklace", "bracelet", "戒指", "项链", "手镯"],
    "bags": ["bag", "backpack", "handbag", "包", "背包", "手提包"],
    "shoes": ["shoe", "boot", "sneaker", "鞋", "靴子", "运动鞋"]
  };
  
  for (const [category, keywords] of Object.entries(categoryKeywords)) {
    if (keywords.some(keyword => nameLower.includes(keyword))) {
      categories.push(category);
    }
  }
  
  return categories.length > 0 ? categories : ["others"];
}

// Generate product description from attributes
function generateDescription(attributes: Record<string, any>): string {
  const relevantAttrs = filterRelevantAttributes(attributes);
  const descriptions: string[] = [];
  
  // Add key specifications
  for (const [key, value] of Object.entries(relevantAttrs)) {
    if (typeof value === "string" && value.length < 100) {
      descriptions.push(`${key}: ${value}`);
    }
  }
  
  return descriptions.slice(0, 5).join(" | ") || "High-quality product sourced from verified supplier.";
}

// Transform Alibaba variants
function transformVariants(variants: any): any[] {
  if (!variants) return [];

  let variantData: any[] = [];

  // Handle both array and object formats
  if (Array.isArray(variants)) {
    variantData = variants;
  } else if (variants.variants && Array.isArray(variants.variants)) {
    variantData = variants.variants;
  }

  return variantData
    .map((variant, index) => {
      if (!variant.variantName) return null;

      // Parse price (can be number, string, or array)
      let price = 0;
      if (typeof variant.price === 'number') {
        price = variant.price;
      } else if (typeof variant.price === 'string') {
        price = parseFloat(variant.price) || 0;
      } else if (Array.isArray(variant.price) && variant.price.length > 0) {
        price = parseFloat(variant.price[0]) || 0;
      }

      // Parse available quantity
      let availableQuantity = 0;
      if (typeof variant.availableQuantity === 'number') {
        availableQuantity = variant.availableQuantity;
      } else if (typeof variant.availableQuantity === 'string') {
        availableQuantity = parseInt(variant.availableQuantity) || 0;
      }

      const serviceFee = Math.max(price * 0.15, 5); // 15% service fee

      return {
        variantName: variant.variantName,
        variantType: variant.variantType || "Option",
        priceInYuan: price,
        serviceFee,
        finalPrice: price + serviceFee,
        stockCount: availableQuantity,
        availableQuantity,
        minQuantity: variant.minQuantity || 1,
        specifications: {
          weight: extractWeight(variant.weight, variant.weightUnit),
          customAttributes: filterRelevantAttributes(variant.sizeInfo || {})
        },
        status: availableQuantity > 0 ? "active" : "out_of_stock",
        isDefault: index === 0, // First variant is default
        sourceMetadata: {
          originalVariantId: variant.variantId || `variant_${index}`,
          lastSyncedAt: Date.now()
        }
      };
    })
    .filter(variant => variant !== null);
}

// Main transformation function
export function transformAlibabaProduct(alibabaProduct: AlibabaProduct, alibabaId: string) {
  const images = validateAndProcessImages([
    ...(alibabaProduct.productImagePreviews || []),
    ...(alibabaProduct.productImageDescriptions || [])
  ]);

  const pricingTiers = transformOffers(alibabaProduct.offers || alibabaProduct.offerList);
  const variants = transformVariants(alibabaProduct.variants);

  // If we have variants, use the default variant's price, otherwise use base pricing
  let basePrice = 0;
  if (variants.length > 0) {
    const defaultVariant = variants.find(v => v.isDefault) || variants[0];
    basePrice = defaultVariant.priceInYuan;
  } else if (pricingTiers.length > 0) {
    basePrice = pricingTiers[0].pricePerUnit;
  }

  const serviceFee = Math.max(basePrice * 0.15, 5);

  return {
    // Base product info
    title: alibabaProduct.name,
    description: generateDescription(alibabaProduct.attributes || {}),
    categories: alibabaProduct.categories || deriveCategoriesFromName(alibabaProduct.name),
    priceInYuan: basePrice,
    serviceFee,
    finalPrice: basePrice + serviceFee,

    // Product-level specifications (common to all variants)
    specifications: {
      weight: variants.length === 0 ? extractWeight(alibabaProduct.weight, alibabaProduct.weightUnit) : undefined,
      dimensions: extractDimensions(alibabaProduct.attributes || {}),
      material: alibabaProduct.attributes?.material || alibabaProduct.attributes?.["材质"],
      color: variants.length === 0 ? (alibabaProduct.attributes?.color || alibabaProduct.attributes?.["颜色"]) : undefined,
      customAttributes: filterRelevantAttributes(alibabaProduct.attributes || {})
    },

    pricingTiers: variants.length === 0 ? pricingTiers : [], // Product-level tiers only if no variants
    customizationOptions: transformCustomServices(alibabaProduct.customServices || []),
    images,

    sourceMetadata: {
      originalUrl: alibabaProduct.productUrl,
      sourceId: alibabaId,
      lastSyncedAt: Date.now(),
      dataQuality: assessDataQuality(alibabaProduct)
    },

    // Variants data for separate processing
    variants
  };
}

// Validation function for transformed product data
function validateTransformedProduct(product: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!product.title || product.title.length < 3) {
    errors.push("Product title is required and must be at least 3 characters");
  }

  if (!product.images || product.images.length === 0) {
    errors.push("At least one valid image is required");
  }

  if (!product.pricingTiers || product.pricingTiers.length === 0) {
    errors.push("At least one pricing tier is required");
  }

  if (product.pricingTiers && product.pricingTiers.some((tier: any) => tier.pricePerUnit <= 0)) {
    errors.push("All pricing tiers must have positive prices");
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Internal mutation to create or find supplier
export const createOrFindSupplier = internalMutation({
  args: {
    name: v.string(),
    platformUrl: v.optional(v.string()),
    platform: v.optional(v.string()),
    createdBy: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Try to find existing supplier by platform URL or name
    let supplier = null;

    if (args.platformUrl) {
      supplier = await ctx.db
        .query("suppliers")
        .filter(q => q.eq(q.field("platformUrl"), args.platformUrl))
        .first();
    }

    if (!supplier && args.name) {
      supplier = await ctx.db
        .query("suppliers")
        .filter(q => q.eq(q.field("name"), args.name))
        .first();
    }

    if (supplier) {
      return supplier._id;
    }

    // Create new supplier
    const supplierId = await ctx.db.insert("suppliers", {
      name: args.name,
      contactInfo: {},
      platformUrl: args.platformUrl,
      isActive: true,
      createdBy: args.createdBy,
      platform: args.platform || "alibaba",
      verificationStatus: "pending",
      shippingRegions: ["Global"],
      paymentMethods: ["T/T", "PayPal"],
    });

    return supplierId;
  },
});

// Internal mutation to import a single product
export const importSingleProduct = internalMutation({
  args: {
    productData: v.object({
      title: v.string(),
      description: v.string(),
      categories: v.array(v.string()),
      specifications: v.object({
        weight: v.optional(v.object({
          value: v.number(),
          unit: v.string(),
        })),
        dimensions: v.optional(v.object({
          length: v.optional(v.number()),
          width: v.optional(v.number()),
          height: v.optional(v.number()),
          unit: v.string(),
        })),
        material: v.optional(v.string()),
        color: v.optional(v.string()),
        customAttributes: v.object({}),
      }),
      pricingTiers: v.array(v.object({
        minQuantity: v.number(),
        pricePerUnit: v.number(),
        currency: v.string(),
        description: v.optional(v.string()),
      })),
      customizationOptions: v.array(v.object({
        name: v.string(),
        type: v.string(),
        additionalCost: v.optional(v.number()),
        minQuantity: v.optional(v.number()),
        description: v.optional(v.string()),
      })),
      images: v.array(v.string()),
      sourceMetadata: v.object({
        originalUrl: v.optional(v.string()),
        sourceId: v.optional(v.string()),
        lastSyncedAt: v.optional(v.number()),
        dataQuality: v.optional(v.union(
          v.literal("high"),
          v.literal("medium"),
          v.literal("low"),
          v.literal("needs_review")
        )),
      }),
    }),
    supplierId: v.id("suppliers"),
    createdBy: v.id("users"),
    curationNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Calculate pricing from tiers
    const basePricing = args.productData.pricingTiers[0];
    if (!basePricing) {
      throw new Error("No pricing tiers available");
    }

    const priceInYuan = basePricing.currency === "CNY" ? basePricing.pricePerUnit : basePricing.pricePerUnit * 7.2; // Rough conversion
    const serviceFee = Math.max(priceInYuan * 0.15, 5); // 15% service fee, minimum 5 yuan
    const finalPrice = priceInYuan + serviceFee;

    // Create the product
    const productId = await ctx.db.insert("products", {
      title: args.productData.title,
      description: args.productData.description,
      curationNotes: args.curationNotes || "Imported from external source - requires curation review",
      supplierId: args.supplierId,
      priceInYuan,
      serviceFee,
      finalPrice,
      tags: args.productData.categories,
      images: args.productData.images,
      stockCount: 100, // Default stock count
      status: args.productData.sourceMetadata.dataQuality === "needs_review" ? "inactive" : "active",
      createdBy: args.createdBy,
      updatedBy: args.createdBy,
      categories: args.productData.categories,
      specifications: args.productData.specifications,
      pricingTiers: args.productData.pricingTiers,
      customizationOptions: args.productData.customizationOptions,
      sourceMetadata: args.productData.sourceMetadata,
    });

    return productId;
  },
});

// Main batch import action
export const importAlibabaProductsBatch = action({
  args: {
    products: v.array(v.any()), // Raw Alibaba product data
    batchSize: v.optional(v.number()),
    supplierName: v.optional(v.string()),
    supplierPlatformUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Check permissions (this would need to be implemented differently in actions)
    // For now, we'll assume the user has permission

    const batchSize = args.batchSize || 10;
    const results = {
      total: args.products.length,
      successful: 0,
      failed: 0,
      errors: [] as Array<{ productId: string; error: string }>,
      productIds: [] as string[]
    };

    // Create or find supplier
    const supplierName = args.supplierName || "Alibaba Supplier";
    const supplierId = await ctx.runMutation(api.dataImport.createOrFindSupplier, {
      name: supplierName,
      platformUrl: args.supplierPlatformUrl,
      platform: "alibaba",
      createdBy: userId,
    });

    // Process products in batches
    for (let i = 0; i < args.products.length; i += batchSize) {
      const batch = args.products.slice(i, i + batchSize);

      for (const [index, rawProduct] of batch.entries()) {
        try {
          const alibabaId = `alibaba_${i + index}_${Date.now()}`;

          // Transform the product data
          const transformedProduct = transformAlibabaProduct(rawProduct, alibabaId);

          // Validate the transformed data
          const validation = validateTransformedProduct(transformedProduct);
          if (!validation.isValid) {
            results.failed++;
            results.errors.push({
              productId: alibabaId,
              error: `Validation failed: ${validation.errors.join(", ")}`
            });
            continue;
          }

          // Import the product
          const productId = await ctx.runMutation(api.dataImport.importSingleProduct, {
            productData: transformedProduct,
            supplierId,
            createdBy: userId,
            curationNotes: `Imported from Alibaba - Quality: ${transformedProduct.sourceMetadata.dataQuality}`
          });

          // Import variants if they exist
          if (transformedProduct.variants && transformedProduct.variants.length > 0) {
            await ctx.runMutation(api.productVariants.createVariantsForProduct, {
              productId,
              variants: transformedProduct.variants,
              createdBy: userId,
            });
          }

          results.successful++;
          results.productIds.push(productId);

        } catch (error) {
          results.failed++;
          results.errors.push({
            productId: `batch_${i + index}`,
            error: error instanceof Error ? error.message : "Unknown error"
          });
        }
      }

      // Add a small delay between batches to avoid overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return results;
  },
});

// Query to get import statistics
export const getImportStats = internalQuery({
  args: {},
  handler: async (ctx) => {
    const products = await ctx.db.query("products").collect();
    const importedProducts = products.filter(p => p.sourceMetadata?.sourceId);

    const qualityStats = {
      high: 0,
      medium: 0,
      low: 0,
      needs_review: 0
    };

    importedProducts.forEach(product => {
      const quality = product.sourceMetadata?.dataQuality || "needs_review";
      qualityStats[quality as keyof typeof qualityStats]++;
    });

    return {
      totalProducts: products.length,
      importedProducts: importedProducts.length,
      qualityDistribution: qualityStats,
      recentImports: importedProducts
        .sort((a, b) => (b.sourceMetadata?.lastSyncedAt || 0) - (a.sourceMetadata?.lastSyncedAt || 0))
        .slice(0, 10)
        .map(p => ({
          id: p._id,
          title: p.title,
          quality: p.sourceMetadata?.dataQuality,
          importedAt: p.sourceMetadata?.lastSyncedAt,
          status: p.status
        }))
    };
  },
});

// Action to create group buys from imported products with multiple pricing tiers
export const createGroupBuysFromImports = action({
  args: {
    productIds: v.optional(v.array(v.id("products"))),
    minTiers: v.optional(v.number()), // Minimum number of pricing tiers required
  },
  handler: async (ctx, args) => {
    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const minTiers = args.minTiers || 2;
    let productIds = args.productIds;

    // If no specific products provided, find all imported products with multiple pricing tiers
    if (!productIds) {
      const products = await ctx.runQuery(api.products.getProducts, {});
      productIds = products.page
        .filter((p: any) => p.pricingTiers && p.pricingTiers.length >= minTiers)
        .map((p: any) => p._id);
    }

    const results = {
      created: 0,
      skipped: 0,
      errors: [] as Array<{ productId: string; error: string }>
    };

    for (const productId of productIds || []) {
      try {
        const product = await ctx.runQuery(api.products.getProduct, { id: productId });

        if (!product || !product.pricingTiers || product.pricingTiers.length < minTiers) {
          results.skipped++;
          continue;
        }

        // Check if group buy already exists for this product
        const existingGroupBuy = await ctx.runQuery(api.groupBuyUtils.getByProduct, { productId });
        if (existingGroupBuy) {
          results.skipped++;
          continue;
        }

        // Create group buy campaign
        const targetTiers = product.pricingTiers.map((tier: any) => ({
          quantity: tier.minQuantity,
          price: tier.pricePerUnit,
          currency: tier.currency,
          description: tier.description
        }));

        const endTime = Date.now() + (30 * 24 * 60 * 60 * 1000); // 30 days from now

        await ctx.runMutation(api.groupBuyUtils.create, {
          productId,
          title: `Group Buy: ${product.title}`,
          description: `Save more when you buy together! Unlock better prices with higher quantities.`,
          targetTiers,
          endTime,
          maxParticipants: 1000,
          autoComplete: true
        });

        results.created++;

      } catch (error) {
        results.errors.push({
          productId,
          error: error instanceof Error ? error.message : "Unknown error"
        });
      }
    }

    return results;
  },
});
