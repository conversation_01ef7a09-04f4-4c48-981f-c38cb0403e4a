import { v } from "convex/values";
import { action, internalMutation } from "./_generated/server";
import { api } from "./_generated/api";
import { auth } from "./auth";

// Types for image processing
type ImageValidationResult = {
  url: string;
  isValid: boolean;
  error?: string;
  dimensions?: { width: number; height: number };
  fileSize?: number;
  contentType?: string;
};

type ImageProcessingResult = {
  originalUrl: string;
  processedUrl?: string;
  thumbnailUrl?: string;
  isValid: boolean;
  error?: string;
  metadata?: {
    width: number;
    height: number;
    fileSize: number;
    contentType: string;
  };
};

// Action to validate and process images from Alibaba data
export const processProductImages = action({
  args: {
    imageUrls: v.array(v.string()),
    productId: v.optional(v.id("products")),
    maxImages: v.optional(v.number()),
  },
  handler: async (ctx, { imageUrls, productId, maxImages = 10 }) => {
    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const results: ImageProcessingResult[] = [];
    const validImages: string[] = [];
    
    // Limit the number of images to process
    const imagesToProcess = imageUrls.slice(0, maxImages);
    
    for (const imageUrl of imagesToProcess) {
      try {
        // Validate the image URL
        const validation = await validateImageUrl(imageUrl);
        
        if (validation.isValid) {
          validImages.push(imageUrl);
          results.push({
            originalUrl: imageUrl,
            processedUrl: imageUrl, // For now, we'll use the original URL
            isValid: true,
            metadata: validation.dimensions && validation.fileSize && validation.contentType ? {
              width: validation.dimensions.width,
              height: validation.dimensions.height,
              fileSize: validation.fileSize,
              contentType: validation.contentType,
            } : undefined,
          });
        } else {
          results.push({
            originalUrl: imageUrl,
            isValid: false,
            error: validation.error || "Image validation failed",
          });
        }
      } catch (error) {
        results.push({
          originalUrl: imageUrl,
          isValid: false,
          error: error instanceof Error ? error.message : "Unknown error during image processing",
        });
      }
    }

    // If productId is provided, update the product with valid images
    if (productId && validImages.length > 0) {
      await ctx.runMutation(api.imageProcessing.updateProductImages, {
        productId,
        imageUrls: validImages,
      });
    }

    return {
      totalProcessed: imagesToProcess.length,
      validImages: validImages.length,
      invalidImages: results.filter(r => !r.isValid).length,
      results,
    };
  },
});

// Helper function to validate image URLs
async function validateImageUrl(url: string): Promise<ImageValidationResult> {
  try {
    // Basic URL validation
    const urlObj = new URL(url);
    if (!urlObj.protocol.startsWith('http')) {
      return {
        url,
        isValid: false,
        error: "Invalid protocol - only HTTP/HTTPS supported",
      };
    }

    // Check if URL looks like an image
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
    const hasImageExtension = imageExtensions.some(ext => 
      url.toLowerCase().includes(ext)
    );

    if (!hasImageExtension) {
      // Try to fetch headers to check content type
      try {
        const response = await fetch(url, { method: 'HEAD' });
        const contentType = response.headers.get('content-type');
        
        if (!contentType || !contentType.startsWith('image/')) {
          return {
            url,
            isValid: false,
            error: "URL does not point to an image",
          };
        }
      } catch (fetchError) {
        return {
          url,
          isValid: false,
          error: "Unable to verify image URL",
        };
      }
    }

    // For now, we'll consider the image valid if it passes basic checks
    // In a production environment, you might want to:
    // 1. Actually fetch the image to verify it loads
    // 2. Check image dimensions and file size
    // 3. Scan for inappropriate content
    // 4. Generate thumbnails

    return {
      url,
      isValid: true,
    };

  } catch (error) {
    return {
      url,
      isValid: false,
      error: error instanceof Error ? error.message : "Invalid URL format",
    };
  }
}

// Internal mutation to update product images
export const updateProductImages = internalMutation({
  args: {
    productId: v.id("products"),
    imageUrls: v.array(v.string()),
  },
  handler: async (ctx, { productId, imageUrls }) => {
    const product = await ctx.db.get(productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // Update the product with the validated images
    await ctx.db.patch(productId, {
      images: imageUrls,
    });

    return { success: true, imageCount: imageUrls.length };
  },
});

// Action to batch process images for multiple products
export const batchProcessProductImages = action({
  args: {
    productIds: v.array(v.id("products")),
    maxImagesPerProduct: v.optional(v.number()),
  },
  handler: async (ctx, { productIds, maxImagesPerProduct = 5 }) => {
    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const results = {
      totalProducts: productIds.length,
      processedProducts: 0,
      totalImagesProcessed: 0,
      totalValidImages: 0,
      errors: [] as Array<{ productId: string; error: string }>,
    };

    for (const productId of productIds) {
      try {
        const product = await ctx.runQuery(api.products.getProduct, { id: productId });
        if (!product) {
          results.errors.push({
            productId,
            error: "Product not found",
          });
          continue;
        }

        // Extract image URLs from source metadata if available
        let imageUrls: string[] = [];
        
        // Try to get images from the current product
        if (product.images && product.images.length > 0) {
          imageUrls = product.images.filter((img: any) => typeof img === 'string');
        }

        // If no images or we need to reprocess, we would need access to the original Alibaba data
        // For now, we'll work with existing images
        if (imageUrls.length === 0) {
          results.errors.push({
            productId,
            error: "No images found to process",
          });
          continue;
        }

        const processingResult = await ctx.runAction(api.imageProcessing.processProductImages, {
          imageUrls,
          productId,
          maxImages: maxImagesPerProduct,
        });

        results.processedProducts++;
        results.totalImagesProcessed += processingResult.totalProcessed;
        results.totalValidImages += processingResult.validImages;

      } catch (error) {
        results.errors.push({
          productId,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return results;
  },
});

// Action to generate image embeddings for search functionality
export const generateImageEmbeddings = action({
  args: {
    productIds: v.array(v.id("products")),
    batchSize: v.optional(v.number()),
  },
  handler: async (ctx, { productIds, batchSize = 5 }) => {
    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const results = {
      totalProducts: productIds.length,
      processedProducts: 0,
      errors: [] as Array<{ productId: string; error: string }>,
    };

    // Process in batches to avoid overwhelming the embedding service
    for (let i = 0; i < productIds.length; i += batchSize) {
      const batch = productIds.slice(i, i + batchSize);
      
      for (const productId of batch) {
        try {
          const product = await ctx.runQuery(api.products.getProduct, { id: productId });
          if (!product || !product.images || product.images.length === 0) {
            results.errors.push({
              productId,
              error: "Product not found or has no images",
            });
            continue;
          }

          // Get the first valid image URL
          const imageUrl = product.images.find((img: any) => typeof img === 'string' && img.startsWith('http'));
          if (!imageUrl) {
            results.errors.push({
              productId,
              error: "No valid image URLs found",
            });
            continue;
          }

          // Generate embedding using the existing embedding system
          // Note: This assumes you have an embeddings module - you may need to create it or remove this
          // For now, we'll skip embedding generation
          results.errors.push({
            productId,
            error: "Image embedding generation not implemented yet",
          });

        } catch (error) {
          results.errors.push({
            productId,
            error: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      // Add delay between batches
      if (i + batchSize < productIds.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  },
});

// Utility function to extract image URLs from Alibaba product data
export function extractReliableImageUrls(alibabaProduct: any): string[] {
  const imageUrls: string[] = [];
  
  // Primary source: productImagePreviews
  if (alibabaProduct.productImagePreviews && Array.isArray(alibabaProduct.productImagePreviews)) {
    imageUrls.push(...alibabaProduct.productImagePreviews);
  }
  
  // Secondary source: productImageDescriptions
  if (alibabaProduct.productImageDescriptions && Array.isArray(alibabaProduct.productImageDescriptions)) {
    imageUrls.push(...alibabaProduct.productImageDescriptions);
  }
  
  // Filter out invalid URLs and remove duplicates
  const validUrls = imageUrls
    .filter(url => url && typeof url === 'string' && url.startsWith('http'))
    .filter((url, index, array) => array.indexOf(url) === index); // Remove duplicates
  
  return validUrls;
}
