import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

// The schema is normally optional, but Convex Auth
// requires indexes defined on `authTables`.
// The schema provides more precise TypeScript types.
export default defineSchema({
  ...authTables,

  // Admin roles and permissions
  adminUsers: defineTable({
    userId: v.id("users"),
    role: v.union(v.literal("super_admin"), v.literal("admin"), v.literal("moderator")),
    permissions: v.array(v.string()),
    createdBy: v.id("users"),
    isActive: v.boolean(),
    lastLoginAt: v.optional(v.number()),
  }).index("by_user", ["userId"])
    .index("by_role", ["role"])
    .index("by_active", ["isActive"]),

  // Products table as per vision document
  products: defineTable({
    title: v.string(),
    description: v.string(),
    curationNotes: v.string(),
    supplierId: v.id("suppliers"),
    priceInYuan: v.number(),
    serviceFee: v.number(),
    finalPrice: v.number(),
    tags: v.array(v.string()),
    images: v.array(v.union(v.string(), v.id("_storage"))),
    imageEmbedding: v.optional(v.array(v.number())),
    stockCount: v.number(),
    status: v.union(v.literal("active"), v.literal("inactive"), v.literal("archived")),
    createdBy: v.id("users"),
    updatedBy: v.id("users"),

    // Business-focused extensions for enhanced functionality
    categories: v.array(v.string()), // Product categorization for filtering/search
    specifications: v.object({       // Structured product specifications
      weight: v.optional(v.object({
        value: v.number(),
        unit: v.string(), // "g", "kg", etc.
      })),
      dimensions: v.optional(v.object({
        length: v.optional(v.number()),
        width: v.optional(v.number()),
        height: v.optional(v.number()),
        unit: v.string(), // "cm", "mm", etc.
      })),
      material: v.optional(v.string()),
      color: v.optional(v.string()),
      customAttributes: v.object({}), // Dynamic key-value pairs for flexible specs
    }),

    // Group buy pricing support
    pricingTiers: v.array(v.object({
      minQuantity: v.number(),
      pricePerUnit: v.number(),
      currency: v.string(),
      description: v.optional(v.string()), // e.g., "pieces", "sets"
    })),

    // Customization services offered
    customizationOptions: v.array(v.object({
      name: v.string(),
      type: v.string(), // "logo", "color", "size", etc.
      additionalCost: v.optional(v.number()),
      minQuantity: v.optional(v.number()),
      description: v.optional(v.string()),
    })),

    // Source tracking (provider-agnostic)
    sourceMetadata: v.object({
      originalUrl: v.optional(v.string()),
      sourceId: v.optional(v.string()), // External ID from any provider
      lastSyncedAt: v.optional(v.number()),
      dataQuality: v.optional(v.union(
        v.literal("high"),
        v.literal("medium"),
        v.literal("low"),
        v.literal("needs_review")
      )),
    }),
  }).index("by_supplier", ["supplierId"])
    .index("by_status", ["status"])
    .index("by_created_by", ["createdBy"])
    .index("by_categories", ["categories"])
    .index("by_data_quality", ["sourceMetadata.dataQuality"]),

  // Suppliers table
  suppliers: defineTable({
    name: v.string(),
    contactInfo: v.object({
      email: v.optional(v.string()),
      phone: v.optional(v.string()),
      address: v.optional(v.string()),
    }),
    platformUrl: v.optional(v.string()),
    rating: v.optional(v.number()),
    notes: v.optional(v.string()),
    isActive: v.boolean(),
    createdBy: v.id("users"),

    // Enhanced supplier information
    platform: v.optional(v.string()), // "alibaba", "1688", "taobao", etc.
    verificationStatus: v.optional(v.union(
      v.literal("verified"),
      v.literal("pending"),
      v.literal("unverified")
    )),
    businessLicense: v.optional(v.string()),
    minimumOrderQuantity: v.optional(v.number()),
    leadTime: v.optional(v.object({
      min: v.number(), // days
      max: v.number(), // days
    })),
    shippingRegions: v.array(v.string()), // Countries/regions they ship to
    paymentMethods: v.array(v.string()),

    // Performance metrics
    metrics: v.optional(v.object({
      totalOrders: v.number(),
      onTimeDeliveryRate: v.number(), // 0-1
      qualityRating: v.number(), // 0-5
      responseTime: v.number(), // hours
      lastPerformanceUpdate: v.number(),
    })),
  }).index("by_active", ["isActive"])
    .index("by_created_by", ["createdBy"])
    .index("by_platform", ["platform"])
    .index("by_verification_status", ["verificationStatus"]),

  // Orders table as per vision document
  orders: defineTable({
    userId: v.id("users"),
    items: v.array(v.object({
      productId: v.id("products"),
      quantity: v.number(),
      priceAtTime: v.number(),
      title: v.string(),
    })),
    status: v.union(
      v.literal("new"),
      v.literal("sourcing"),
      v.literal("action_required"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled")
    ),
    trackingNumber: v.optional(v.string()),
    shippingAddress: v.object({
      name: v.string(),
      address: v.string(),
      city: v.string(),
      country: v.string(),
      postalCode: v.string(),
    }),
    communicationHistory: v.array(v.object({
      message: v.string(),
      fromAdmin: v.boolean(),
      adminUserId: v.optional(v.id("users")),
      timestamp: v.number(),
    })),
    issueResolution: v.optional(v.object({
      issue: v.string(),
      suggestedAlternatives: v.array(v.id("products")),
      resolution: v.optional(v.string()),
    })),
    totalAmount: v.number(),
    assignedTo: v.optional(v.id("users")),
  }).index("by_user", ["userId"])
    .index("by_status", ["status"])
    .index("by_assigned_to", ["assignedTo"]),

  // Group buys table as per vision document
  groupBuys: defineTable({
    productId: v.id("products"),
    targetTiers: v.array(v.object({
      quantity: v.number(),
      price: v.number(),
      currency: v.string(),
      description: v.optional(v.string()), // "pieces", "sets", etc.
    })),
    currentParticipants: v.number(),
    currentTier: v.optional(v.object({
      quantity: v.number(),
      price: v.number(),
      currency: v.string(),
    })),
    status: v.union(v.literal("active"), v.literal("completed"), v.literal("expired")),
    startTime: v.number(),
    endTime: v.number(),
    createdBy: v.id("users"),

    // Enhanced group buy features
    title: v.string(),
    description: v.optional(v.string()),
    maxParticipants: v.optional(v.number()),
    autoComplete: v.boolean(), // Auto-complete when target reached
    participants: v.array(v.object({
      userId: v.id("users"),
      quantity: v.number(),
      joinedAt: v.number(),
      status: v.union(v.literal("active"), v.literal("completed"), v.literal("cancelled")),
    })),
  }).index("by_product", ["productId"])
    .index("by_status", ["status"])
    .index("by_end_time", ["endTime"])
    .index("by_created_by", ["createdBy"]),

  // Product variants table for handling different options (colors, sizes, etc.)
  productVariants: defineTable({
    productId: v.id("products"),
    variantName: v.string(), // e.g., "Red", "Large", "32GB"
    variantType: v.string(), // e.g., "Color", "Size", "Storage"

    // Pricing for this specific variant
    priceInYuan: v.number(),
    serviceFee: v.number(),
    finalPrice: v.number(),

    // Inventory for this variant
    stockCount: v.number(),
    availableQuantity: v.optional(v.number()),
    minQuantity: v.optional(v.number()),

    // Variant-specific specifications
    specifications: v.optional(v.object({
      weight: v.optional(v.object({
        value: v.number(),
        unit: v.string(),
      })),
      dimensions: v.optional(v.object({
        length: v.optional(v.number()),
        width: v.optional(v.number()),
        height: v.optional(v.number()),
        unit: v.string(),
      })),
      customAttributes: v.optional(v.object({})),
    })),

    // Variant-specific pricing tiers (for group buys)
    pricingTiers: v.optional(v.array(v.object({
      minQuantity: v.number(),
      pricePerUnit: v.number(),
      currency: v.string(),
      description: v.optional(v.string()),
    }))),

    // Images specific to this variant
    images: v.optional(v.array(v.union(v.string(), v.id("_storage")))),

    // Status
    status: v.union(v.literal("active"), v.literal("inactive"), v.literal("out_of_stock")),
    isDefault: v.boolean(), // Is this the default variant shown?

    // Source tracking
    sourceMetadata: v.optional(v.object({
      originalVariantId: v.optional(v.string()),
      lastSyncedAt: v.optional(v.number()),
    })),

    createdBy: v.id("users"),
    updatedBy: v.id("users"),
  }).index("by_product", ["productId"])
    .index("by_product_and_type", ["productId", "variantType"])
    .index("by_status", ["status"])
    .index("by_default", ["isDefault"]),

  // Remove the test numbers table
});
