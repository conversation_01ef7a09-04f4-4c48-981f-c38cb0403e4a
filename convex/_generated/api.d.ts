/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth_permissions from "../auth/permissions.js";
import type * as auth_rbac from "../auth/rbac.js";
import type * as auth_setup from "../auth/setup.js";
import type * as auth from "../auth.js";
import type * as dataImport from "../dataImport.js";
import type * as embeddings from "../embeddings.js";
import type * as files from "../files.js";
import type * as groupBuyUtils from "../groupBuyUtils.js";
import type * as groupBuys from "../groupBuys.js";
import type * as http from "../http.js";
import type * as imageProcessing from "../imageProcessing.js";
import type * as orders from "../orders.js";
import type * as productVariants from "../productVariants.js";
import type * as products from "../products.js";
import type * as suppliers from "../suppliers.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  "auth/permissions": typeof auth_permissions;
  "auth/rbac": typeof auth_rbac;
  "auth/setup": typeof auth_setup;
  auth: typeof auth;
  dataImport: typeof dataImport;
  embeddings: typeof embeddings;
  files: typeof files;
  groupBuyUtils: typeof groupBuyUtils;
  groupBuys: typeof groupBuys;
  http: typeof http;
  imageProcessing: typeof imageProcessing;
  orders: typeof orders;
  productVariants: typeof productVariants;
  products: typeof products;
  suppliers: typeof suppliers;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
