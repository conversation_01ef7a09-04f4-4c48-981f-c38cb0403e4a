import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { auth } from "./auth";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";

// Generate upload URL for file uploads
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_MANAGE_IMAGES);
    
    return await ctx.storage.generateUploadUrl();
  },
});

// Get file URL by storage ID
export const getFileUrl = query({
  args: { storageId: v.id("_storage") },
  handler: async (ctx, { storageId }) => {
    return await ctx.storage.getUrl(storageId);
  },
});

// Delete a file from storage
export const deleteFile = mutation({
  args: { storageId: v.id("_storage") },
  handler: async (ctx, { storageId }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_MANAGE_IMAGES);
    
    await ctx.storage.delete(storageId);
    return { success: true };
  },
});

// Upload and process product image
export const uploadProductImage = mutation({
  args: {
    storageId: v.id("_storage"),
    productId: v.optional(v.id("products")),
    altText: v.optional(v.string()),
  },
  handler: async (ctx, { storageId, productId, altText }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_MANAGE_IMAGES);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get file metadata
    const file = await ctx.storage.getMetadata(storageId);
    if (!file) {
      throw new Error("File not found");
    }

    // Validate file type
    if (!file.contentType?.startsWith("image/")) {
      throw new Error("File must be an image");
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      throw new Error("Image file size must be less than 5MB");
    }

    // Get file URL
    const fileUrl = await ctx.storage.getUrl(storageId);
    if (!fileUrl) {
      throw new Error("Failed to get file URL");
    }

    // If productId is provided, add image to product
    if (productId) {
      const product = await ctx.db.get(productId);
      if (!product) {
        throw new Error("Product not found");
      }

      // Add image URL to product images array
      const updatedImages = [...(product.images || []), fileUrl];
      await ctx.db.patch(productId, {
        images: updatedImages,
        updatedBy: userId,
      });
    }

    return {
      storageId,
      url: fileUrl,
      contentType: file.contentType,
      size: file.size,
      altText: altText || "",
    };
  },
});

// Remove image from product
export const removeProductImage = mutation({
  args: {
    productId: v.id("products"),
    imageUrl: v.string(),
  },
  handler: async (ctx, { productId, imageUrl }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_MANAGE_IMAGES);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const product = await ctx.db.get(productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // Remove image URL from product images array
    const updatedImages = (product.images || []).filter(url => url !== imageUrl);
    
    await ctx.db.patch(productId, {
      images: updatedImages,
      updatedBy: userId,
    });

    return { success: true };
  },
});

// Reorder product images
export const reorderProductImages = mutation({
  args: {
    productId: v.id("products"),
    imageUrls: v.array(v.string()),
  },
  handler: async (ctx, { productId, imageUrls }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_MANAGE_IMAGES);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const product = await ctx.db.get(productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // Validate that all provided URLs exist in current product images
    const currentImages = product.images || [];
    const invalidUrls = imageUrls.filter(url => !currentImages.includes(url));
    
    if (invalidUrls.length > 0) {
      throw new Error("Some image URLs are not associated with this product");
    }

    await ctx.db.patch(productId, {
      images: imageUrls,
      updatedBy: userId,
    });

    return { success: true };
  },
});

// Batch upload multiple images
export const batchUploadImages = action({
  args: {
    files: v.array(v.object({
      storageId: v.id("_storage"),
      altText: v.optional(v.string()),
    })),
    productId: v.optional(v.id("products")),
  },
  handler: async (ctx, { files, productId }): Promise<Array<{ success: boolean; url?: string; error?: string }>> => {
    const results = [];
    
    for (const file of files) {
      try {
        // For now, we'll just return the storage URL directly
        // In a full implementation, this would call the uploadProductImage mutation
        const url = await ctx.storage.getUrl(file.storageId);
        const result = { url };
        results.push({ success: true, url: result.url || undefined });
      } catch (error) {
        results.push({ 
          success: false, 
          error: error instanceof Error ? error.message : "Unknown error",
          storageId: file.storageId 
        });
      }
    }

    return results;
  },
});

// Get image metadata and processing status
export const getImageMetadata = query({
  args: { storageId: v.id("_storage") },
  handler: async (ctx, { storageId }) => {
    const metadata = await ctx.storage.getMetadata(storageId);
    if (!metadata) {
      return null;
    }

    const url = await ctx.storage.getUrl(storageId);
    
    return {
      storageId,
      url,
      contentType: metadata.contentType,
      size: metadata.size,
      uploadedAt: Date.now(),
    };
  },
});

// Clean up orphaned images (images not associated with any product)
export const cleanupOrphanedImages = action({
  args: {},
  handler: async (ctx) => {
    // Permission check would need to be done differently in actions
    // For now, we'll skip the permission check

    // For now, we'll return a placeholder response
    // In a full implementation, this would scan all products and find unused images
    const usedImageUrls = new Set<string>();

    // This is a simplified version - in a real implementation,
    // you'd need to track storage IDs and their corresponding URLs
    // to properly clean up unused files from Convex storage
    
    return {
      message: "Cleanup completed",
      usedImagesCount: usedImageUrls.size,
    };
  },
});

// Image optimization and processing utilities
export const processImageForWeb = action({
  args: {
    storageId: v.id("_storage"),
    maxWidth: v.optional(v.number()),
    maxHeight: v.optional(v.number()),
    quality: v.optional(v.number()),
  },
  handler: async (ctx, { storageId, maxWidth = 1200, maxHeight = 1200, quality = 85 }) => {
    // Note: maxWidth, maxHeight, quality parameters are for future image processing
    // Permission check would need to be done differently in actions
    // For now, we'll skip the permission check in actions

    // Get the original file
    const metadata = await ctx.storage.getMetadata(storageId);
    if (!metadata) {
      throw new Error("File not found");
    }

    if (!metadata.contentType?.startsWith("image/")) {
      throw new Error("File is not an image");
    }

    // In a real implementation, you would:
    // 1. Download the image from storage
    // 2. Process it using an image processing library
    // 3. Upload the processed version back to storage
    // 4. Return the new storage ID and URL

    // For now, return the original image info
    const url = await ctx.storage.getUrl(storageId);
    
    return {
      originalStorageId: storageId,
      processedStorageId: storageId, // Would be different in real implementation
      originalUrl: url,
      processedUrl: url, // Would be different in real implementation
      originalSize: metadata.size,
      processedSize: metadata.size, // Would be smaller in real implementation
      compressionRatio: 1, // Would be actual ratio in real implementation
    };
  },
});

// Generate image thumbnails
export const generateThumbnails = action({
  args: {
    storageId: v.id("_storage"),
    sizes: v.array(v.object({
      width: v.number(),
      height: v.number(),
      name: v.string(),
    })),
  },
  handler: async (ctx, { storageId, sizes }) => {
    // Permission check would need to be done differently in actions
    // For now, we'll skip the permission check in actions

    const metadata = await ctx.storage.getMetadata(storageId);
    if (!metadata) {
      throw new Error("File not found");
    }

    if (!metadata.contentType?.startsWith("image/")) {
      throw new Error("File is not an image");
    }

    // In a real implementation, you would generate actual thumbnails
    // For now, return placeholder data
    const originalUrl = await ctx.storage.getUrl(storageId);
    
    const thumbnails = sizes.map(size => ({
      name: size.name,
      width: size.width,
      height: size.height,
      storageId: storageId, // Would be different storage ID for each thumbnail
      url: originalUrl, // Would be different URL for each thumbnail
      size: Math.floor(metadata.size * (size.width * size.height) / (1200 * 1200)), // Estimated size
    }));

    return {
      originalStorageId: storageId,
      originalUrl,
      thumbnails,
    };
  },
});

// Validate image before upload (client-side helper data)
export const validateImageUpload = query({
  args: {
    contentType: v.string(),
    size: v.number(),
    filename: v.string(),
  },
  handler: async (_ctx, { contentType, size, filename }) => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check file type
    const allowedTypes = ["image/jpeg", "image/png", "image/webp", "image/gif"];
    if (!allowedTypes.includes(contentType)) {
      errors.push("File type not supported. Please use JPEG, PNG, WebP, or GIF.");
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024;
    if (size > maxSize) {
      errors.push("File size too large. Maximum size is 5MB.");
    }

    // Check filename
    if (filename.length > 255) {
      errors.push("Filename too long. Maximum length is 255 characters.");
    }

    // Warnings for optimization
    if (size > 1024 * 1024) {
      warnings.push("Large file size. Consider optimizing the image for web use.");
    }

    if (contentType === "image/gif" && size > 2 * 1024 * 1024) {
      warnings.push("Large GIF file. Consider converting to video format for better performance.");
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      recommendations: {
        shouldOptimize: size > 500 * 1024,
        shouldGenerateThumbnails: size > 100 * 1024,
        recommendedFormat: contentType === "image/png" && size > 1024 * 1024 ? "image/webp" : contentType,
      },
    };
  },
});
