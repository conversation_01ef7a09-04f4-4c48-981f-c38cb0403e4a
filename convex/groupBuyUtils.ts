import { v } from "convex/values";
import { query, mutation, internalQuery } from "./_generated/server";
import { api } from "./_generated/api";
import { auth } from "./auth";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";

// Query to get group buys by product
export const getByProduct = query({
  args: { productId: v.id("products") },
  handler: async (ctx, { productId }) => {
    return await ctx.db
      .query("groupBuys")
      .withIndex("by_product", (q) => q.eq("productId", productId))
      .first();
  },
});

// Query to get active group buys
export const getActiveGroupBuys = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { limit = 20 }) => {
    const groupBuys = await ctx.db
      .query("groupBuys")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .order("desc")
      .take(limit);

    // Enrich with product information
    const enrichedGroupBuys = await Promise.all(
      groupBuys.map(async (groupBuy) => {
        const product = await ctx.db.get(groupBuy.productId);
        
        // Calculate current tier based on participants
        const currentTier = calculateCurrentTier(groupBuy.targetTiers, groupBuy.currentParticipants);
        
        return {
          ...groupBuy,
          product: product ? {
            title: product.title,
            images: product.images,
            finalPrice: product.finalPrice,
          } : null,
          currentTier,
          progress: calculateProgress(groupBuy.targetTiers, groupBuy.currentParticipants),
          timeRemaining: Math.max(0, groupBuy.endTime - Date.now()),
        };
      })
    );

    return enrichedGroupBuys.filter(gb => gb.product !== null);
  },
});

// Mutation to create a group buy
export const create = mutation({
  args: {
    productId: v.id("products"),
    title: v.string(),
    description: v.optional(v.string()),
    targetTiers: v.array(v.object({
      quantity: v.number(),
      price: v.number(),
      currency: v.string(),
      description: v.optional(v.string()),
    })),
    endTime: v.number(),
    maxParticipants: v.optional(v.number()),
    autoComplete: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_CREATE);
    
    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Validate product exists
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // Validate end time is in the future
    if (args.endTime <= Date.now()) {
      throw new Error("End time must be in the future");
    }

    // Sort target tiers by quantity
    const sortedTiers = args.targetTiers.sort((a, b) => a.quantity - b.quantity);

    const groupBuyId = await ctx.db.insert("groupBuys", {
      productId: args.productId,
      title: args.title,
      description: args.description,
      targetTiers: sortedTiers,
      currentParticipants: 0,
      status: "active",
      startTime: Date.now(),
      endTime: args.endTime,
      createdBy: userId,
      maxParticipants: args.maxParticipants,
      autoComplete: args.autoComplete || false,
      participants: [],
    });

    return groupBuyId;
  },
});

// Mutation to join a group buy
export const joinGroupBuy = mutation({
  args: {
    groupBuyId: v.id("groupBuys"),
    quantity: v.number(),
  },
  handler: async (ctx, { groupBuyId, quantity }) => {
    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const groupBuy = await ctx.db.get(groupBuyId);
    if (!groupBuy) {
      throw new Error("Group buy not found");
    }

    // Validate group buy is active
    if (groupBuy.status !== "active") {
      throw new Error("Group buy is not active");
    }

    // Check if group buy has expired
    if (groupBuy.endTime <= Date.now()) {
      throw new Error("Group buy has expired");
    }

    // Check if user is already participating
    const existingParticipant = groupBuy.participants.find(p => p.userId === userId);
    if (existingParticipant) {
      throw new Error("You are already participating in this group buy");
    }

    // Check max participants limit
    if (groupBuy.maxParticipants && groupBuy.currentParticipants >= groupBuy.maxParticipants) {
      throw new Error("Group buy is full");
    }

    // Add participant
    const newParticipants = [
      ...groupBuy.participants,
      {
        userId,
        quantity,
        joinedAt: Date.now(),
        status: "active" as const,
      }
    ];

    const newParticipantCount = groupBuy.currentParticipants + quantity;
    const currentTier = calculateCurrentTier(groupBuy.targetTiers, newParticipantCount);

    // Update group buy
    await ctx.db.patch(groupBuyId, {
      participants: newParticipants,
      currentParticipants: newParticipantCount,
      currentTier,
    });

    // Check if auto-complete conditions are met
    if (groupBuy.autoComplete && shouldAutoComplete(groupBuy.targetTiers, newParticipantCount)) {
      await ctx.db.patch(groupBuyId, {
        status: "completed",
      });
    }

    return { success: true, currentTier, totalParticipants: newParticipantCount };
  },
});

// Helper function to calculate current pricing tier
function calculateCurrentTier(targetTiers: any[], currentParticipants: number) {
  if (!targetTiers || targetTiers.length === 0) return null;

  // Find the highest tier that the current participants qualify for
  let currentTier = targetTiers[0]; // Start with the first (lowest quantity) tier
  
  for (const tier of targetTiers) {
    if (currentParticipants >= tier.quantity) {
      currentTier = tier;
    } else {
      break; // Since tiers are sorted by quantity, we can break here
    }
  }

  return currentTier;
}

// Helper function to calculate progress towards next tier
function calculateProgress(targetTiers: any[], currentParticipants: number) {
  if (!targetTiers || targetTiers.length === 0) return { current: 0, next: 0, percentage: 0 };

  const currentTier = calculateCurrentTier(targetTiers, currentParticipants);
  if (!currentTier) return { current: 0, next: 0, percentage: 0 };

  // Find next tier
  const currentTierIndex = targetTiers.findIndex(t => t.quantity === currentTier.quantity);
  const nextTier = currentTierIndex < targetTiers.length - 1 ? targetTiers[currentTierIndex + 1] : null;

  if (!nextTier) {
    // Already at the highest tier
    return {
      current: currentParticipants,
      next: currentTier.quantity,
      percentage: 100,
      isMaxTier: true
    };
  }

  const progress = currentParticipants - currentTier.quantity;
  const needed = nextTier.quantity - currentTier.quantity;
  const percentage = Math.min(100, (progress / needed) * 100);

  return {
    current: currentParticipants,
    next: nextTier.quantity,
    percentage,
    needed: Math.max(0, nextTier.quantity - currentParticipants),
    nextTierSavings: currentTier.price - nextTier.price
  };
}

// Helper function to determine if group buy should auto-complete
function shouldAutoComplete(targetTiers: any[], currentParticipants: number): boolean {
  if (!targetTiers || targetTiers.length === 0) return false;
  
  // Auto-complete if we've reached the highest tier
  const highestTier = targetTiers[targetTiers.length - 1];
  return currentParticipants >= highestTier.quantity;
}

// Query to get group buy analytics
export const getGroupBuyAnalytics = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    const allGroupBuys = await ctx.db.query("groupBuys").collect();
    
    const analytics = {
      total: allGroupBuys.length,
      active: allGroupBuys.filter(gb => gb.status === "active").length,
      completed: allGroupBuys.filter(gb => gb.status === "completed").length,
      expired: allGroupBuys.filter(gb => gb.status === "expired").length,
      totalParticipants: allGroupBuys.reduce((sum, gb) => sum + gb.currentParticipants, 0),
      averageParticipants: allGroupBuys.length > 0 
        ? allGroupBuys.reduce((sum, gb) => sum + gb.currentParticipants, 0) / allGroupBuys.length 
        : 0,
      topPerforming: allGroupBuys
        .sort((a, b) => b.currentParticipants - a.currentParticipants)
        .slice(0, 5)
        .map(gb => ({
          id: gb._id,
          title: gb.title,
          participants: gb.currentParticipants,
          status: gb.status
        }))
    };

    return analytics;
  },
});

// Internal query to get group buys that need to be expired
export const getExpiredGroupBuys = internalQuery({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    return await ctx.db
      .query("groupBuys")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .filter(q => q.lt(q.field("endTime"), now))
      .collect();
  },
});

// Mutation to expire group buys (to be called by a scheduled function)
export const expireGroupBuys = mutation({
  args: {},
  handler: async (ctx) => {
    const expiredGroupBuys = await ctx.runQuery(api.groupBuyUtils.getExpiredGroupBuys, {});
    
    for (const groupBuy of expiredGroupBuys) {
      await ctx.db.patch(groupBuy._id, {
        status: "expired",
      });
    }
    
    return { expired: expiredGroupBuys.length };
  },
});
