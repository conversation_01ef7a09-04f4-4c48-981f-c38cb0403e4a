// convex/auth/permissions.ts

import { v } from "convex/values";

// Define all possible permissions in the system
export const PERMISSIONS = {
  // Product management
  PRODUCTS_VIEW: "products:view",
  PRODUCTS_CREATE: "products:create",
  PRODUCTS_EDIT: "products:edit",
  PRODUCTS_DELETE: "products:delete",
  PRODUCTS_MANAGE_IMAGES: "products:manage_images",
  
  // Order management
  ORDERS_VIEW: "orders:view",
  ORDERS_EDIT: "orders:edit",
  ORDERS_ASSIGN: "orders:assign",
  ORDERS_COMMUNICATE: "orders:communicate",
  ORDERS_CANCEL: "orders:cancel",
  
  // User management
  USERS_VIEW: "users:view",
  USERS_EDIT: "users:edit",
  USERS_DELETE: "users:delete",
  
  // Admin management
  ADMIN_USERS_VIEW: "admin_users:view",
  ADMIN_USERS_CREATE: "admin_users:create",
  ADMIN_USERS_EDIT: "admin_users:edit",
  ADMIN_USERS_DELETE: "admin_users:delete",
  
  // Supplier management
  SUPPLIERS_VIEW: "suppliers:view",
  SUPPLIERS_CREATE: "suppliers:create",
  SUPPLIERS_EDIT: "suppliers:edit",
  SUPPLIERS_DELETE: "suppliers:delete",
  
  // Group buy management
  GROUP_BUYS_VIEW: "group_buys:view",
  GROUP_BUYS_CREATE: "group_buys:create",
  GROUP_BUYS_EDIT: "group_buys:edit",
  GROUP_BUYS_DELETE: "group_buys:delete",
  
  // Analytics and reporting
  ANALYTICS_VIEW: "analytics:view",
  ANALYTICS_EXPORT: "analytics:export",
  
  // System administration
  SYSTEM_SETTINGS: "system:settings",
  SYSTEM_LOGS: "system:logs",
} as const;

// Type definitions
export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];

// FIX: Define the Role type explicitly as a union of string literals.
// This breaks the circular reference. These roles MUST match the keys of the object below.
export type Role = "super_admin" | "admin" | "moderator";

// Define role-based permission sets
// The circular reference error is now resolved because `Role` is defined independently.
export const ROLE_PERMISSIONS: Record<Role, readonly Permission[]> = {
  super_admin: [
    // Full access to everything
    ...Object.values(PERMISSIONS),
  ],
  admin: [
    // Product management
    PERMISSIONS.PRODUCTS_VIEW,
    PERMISSIONS.PRODUCTS_CREATE,
    PERMISSIONS.PRODUCTS_EDIT,
    PERMISSIONS.PRODUCTS_MANAGE_IMAGES,
    
    // Order management
    PERMISSIONS.ORDERS_VIEW,
    PERMISSIONS.ORDERS_EDIT,
    PERMISSIONS.ORDERS_ASSIGN,
    PERMISSIONS.ORDERS_COMMUNICATE,
    
    // User management (view only)
    PERMISSIONS.USERS_VIEW,
    
    // Supplier management
    PERMISSIONS.SUPPLIERS_VIEW,
    PERMISSIONS.SUPPLIERS_CREATE,
    PERMISSIONS.SUPPLIERS_EDIT,
    
    // Group buy management
    PERMISSIONS.GROUP_BUYS_VIEW,
    PERMISSIONS.GROUP_BUYS_CREATE,
    PERMISSIONS.GROUP_BUYS_EDIT,
    
    // Analytics
    PERMISSIONS.ANALYTICS_VIEW,
  ],
  moderator: [
    // Limited product management
    PERMISSIONS.PRODUCTS_VIEW,
    PERMISSIONS.PRODUCTS_EDIT,
    
    // Order management
    PERMISSIONS.ORDERS_VIEW,
    PERMISSIONS.ORDERS_COMMUNICATE,
    
    // User management (view only)
    PERMISSIONS.USERS_VIEW,
    
    // Supplier management (view only)
    PERMISSIONS.SUPPLIERS_VIEW,
    
    // Group buy management (view only)
    PERMISSIONS.GROUP_BUYS_VIEW,
    
    // Analytics (view only)
    PERMISSIONS.ANALYTICS_VIEW,
  ],
} as const;


// Helper function to get permissions for a role
export function getPermissionsForRole(role: Role): readonly Permission[] {
  return ROLE_PERMISSIONS[role];
}

// Helper function to check if a role has a specific permission
export function roleHasPermission(role: Role, permission: Permission): boolean {
  // This cast is safe because we know Permission is a string.
  return (ROLE_PERMISSIONS[role] as readonly string[]).includes(permission);
}

// Validation schemas for Convex
// This validator now perfectly aligns with our explicitly defined Role type.
export const roleValidator = v.union(
  v.literal("super_admin"),
  v.literal("admin"),
  v.literal("moderator")
);

// A more specific validator can be useful, but string is fine for now.
export const permissionValidator = v.string();