// convex/auth/setup.ts

// FIX 1: Import Id for type casting and context types for our helper function.
import { v } from "convex/values";
import { mutation, query, type QueryCtx, type MutationCtx } from "../_generated/server";
import { getPermissionsForRole } from "./permissions";
import { Id } from "../_generated/dataModel";

// FIX 3: Extract the core logic into a reusable helper function.
// This allows us to call it from multiple places without creating a circular dependency.
async function getHasSuperAdmin(ctx: QueryCtx | MutationCtx) {
  const superAdmin = await ctx.db
    .query("adminUsers")
    .withIndex("by_role", (q) => q.eq("role", "super_admin"))
    .filter((q) => q.eq(q.field("isActive"), true))
    .first();
  return !!superAdmin;
}

// The exported query now just wraps the helper function.
export const hasSuperAdmin = query({
  args: {},
  handler: async (ctx) => {
    return getHasSuperAdmin(ctx);
  },
});

export const createFirstSuperAdmin = mutation({
  args: {
    email: v.string(),
    password: v.string(),
    name: v.string(),
  },
  handler: async (ctx, { email, /*password, name*/ }) => {
    const existingSuperAdmin = await getHasSuperAdmin(ctx);
    if (existingSuperAdmin) {
      throw new Error("Super admin already exists");
    }
    
    const existingUser = await ctx.db
      .query("users")
      .withIndex("email", (q) => q.eq("email", email))
      .first();
    
    if (!existingUser) {
      throw new Error(`User with email ${email} must be created first through the authentication system.`);
    }
    
    const adminUserId = await ctx.db.insert("adminUsers", {
      userId: existingUser._id,
      role: "super_admin",
      permissions: [...getPermissionsForRole("super_admin")],
      createdBy: existingUser._id,
      isActive: true,
    });
    
    return {
      success: true,
      adminUserId,
      message: "First super admin created successfully"
    };
  },
});

export const promoteUserToAdmin = mutation({
  args: {
    userId: v.id("users"),
    role: v.union(v.literal("super_admin"), v.literal("admin"), v.literal("moderator")),
  },
  handler: async (ctx, { userId, role }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    // FIX 2: Cast the `identity.subject` string to the `Id<"users">` type.
    const currentUserId = identity.subject as Id<"users">;

    const currentAdmin = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", currentUserId))
      .first();
    
    if (!currentAdmin || currentAdmin.role !== "super_admin" || !currentAdmin.isActive) {
      throw new Error("Only active super admins can promote users");
    }
    
    const existingAdmin = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();
    
    if (existingAdmin) {
      throw new Error("User already has admin privileges");
    }
    
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found");
    }
    
    const adminUserId = await ctx.db.insert("adminUsers", {
      userId,
      role,
      permissions: [...getPermissionsForRole(role)],
      // FIX 2 (Again): Cast the subject string to the expected Id type.
      createdBy: currentUserId,
      isActive: true,
    });
    
    return {
      success: true,
      adminUserId,
      message: `User promoted to ${role} successfully`
    };
  },
});

export const getSetupStatus = query({
  args: {},
  handler: async (ctx): Promise<{
    hasSuperAdmin: boolean;
    totalUsers: number;
    totalAdmins: number;
    needsSetup: boolean;
  }> => {
    // FIX 3 (Again): Call the helper function directly, avoiding `ctx.runQuery` and the `api` object.
    const hasSuperAdminResult = await getHasSuperAdmin(ctx);
    const totalUsers = await ctx.db.query("users").collect();
    const totalAdmins = await ctx.db.query("adminUsers").collect();
    
    return {
      hasSuperAdmin: hasSuperAdminResult,
      totalUsers: totalUsers.length,
      totalAdmins: totalAdmins.length,
      needsSetup: !hasSuperAdminResult,
    };
  },
});