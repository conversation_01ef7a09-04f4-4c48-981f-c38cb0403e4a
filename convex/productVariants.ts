import { v } from "convex/values";
import { query, mutation, internalMutation } from "./_generated/server";
import { auth } from "./auth";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";

// Query to get variants for a product
export const getProductVariants = query({
  args: { productId: v.id("products") },
  handler: async (ctx, { productId }) => {
    const variants = await ctx.db
      .query("productVariants")
      .withIndex("by_product", (q) => q.eq("productId", productId))
      .collect();

    return variants.sort((a, b) => {
      // Default variant first, then by variant type and name
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;
      if (a.variantType !== b.variantType) return a.variantType.localeCompare(b.variantType);
      return a.variantName.localeCompare(b.variantName);
    });
  },
});

// Query to get a specific variant
export const getVariant = query({
  args: { variantId: v.id("productVariants") },
  handler: async (ctx, { variantId }) => {
    return await ctx.db.get(variantId);
  },
});

// Query to get variants by type for a product
export const getVariantsByType = query({
  args: { 
    productId: v.id("products"),
    variantType: v.string()
  },
  handler: async (ctx, { productId, variantType }) => {
    return await ctx.db
      .query("productVariants")
      .withIndex("by_product_and_type", (q) => 
        q.eq("productId", productId).eq("variantType", variantType)
      )
      .collect();
  },
});

// Mutation to create a product variant
export const createVariant = mutation({
  args: {
    productId: v.id("products"),
    variantName: v.string(),
    variantType: v.string(),
    priceInYuan: v.number(),
    serviceFee: v.number(),
    finalPrice: v.number(),
    stockCount: v.number(),
    availableQuantity: v.optional(v.number()),
    minQuantity: v.optional(v.number()),
    specifications: v.optional(v.object({
      weight: v.optional(v.object({
        value: v.number(),
        unit: v.string(),
      })),
      dimensions: v.optional(v.object({
        length: v.optional(v.number()),
        width: v.optional(v.number()),
        height: v.optional(v.number()),
        unit: v.string(),
      })),
      customAttributes: v.optional(v.object({})),
    })),
    pricingTiers: v.optional(v.array(v.object({
      minQuantity: v.number(),
      pricePerUnit: v.number(),
      currency: v.string(),
      description: v.optional(v.string()),
    }))),
    images: v.optional(v.array(v.union(v.string(), v.id("_storage")))),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("out_of_stock"))),
    isDefault: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_CREATE);
    
    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    // Verify product exists
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found");
    }

    // If this is set as default, unset other defaults
    if (args.isDefault) {
      const existingVariants = await ctx.db
        .query("productVariants")
        .withIndex("by_product", (q) => q.eq("productId", args.productId))
        .collect();

      for (const variant of existingVariants) {
        if (variant.isDefault) {
          await ctx.db.patch(variant._id, { isDefault: false });
        }
      }
    }

    const variantId = await ctx.db.insert("productVariants", {
      ...args,
      status: args.status || "active",
      isDefault: args.isDefault || false,
      createdBy: userId,
      updatedBy: userId,
    });

    return variantId;
  },
});

// Mutation to update a product variant
export const updateVariant = mutation({
  args: {
    variantId: v.id("productVariants"),
    variantName: v.optional(v.string()),
    variantType: v.optional(v.string()),
    priceInYuan: v.optional(v.number()),
    serviceFee: v.optional(v.number()),
    finalPrice: v.optional(v.number()),
    stockCount: v.optional(v.number()),
    availableQuantity: v.optional(v.number()),
    minQuantity: v.optional(v.number()),
    specifications: v.optional(v.object({
      weight: v.optional(v.object({
        value: v.number(),
        unit: v.string(),
      })),
      dimensions: v.optional(v.object({
        length: v.optional(v.number()),
        width: v.optional(v.number()),
        height: v.optional(v.number()),
        unit: v.string(),
      })),
      customAttributes: v.optional(v.object({})),
    })),
    pricingTiers: v.optional(v.array(v.object({
      minQuantity: v.number(),
      pricePerUnit: v.number(),
      currency: v.string(),
      description: v.optional(v.string()),
    }))),
    images: v.optional(v.array(v.union(v.string(), v.id("_storage")))),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("out_of_stock"))),
    isDefault: v.optional(v.boolean()),
  },
  handler: async (ctx, { variantId, ...updates }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_EDIT);
    
    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Authentication required");
    }

    const variant = await ctx.db.get(variantId);
    if (!variant) {
      throw new Error("Variant not found");
    }

    // If this is being set as default, unset other defaults
    if (updates.isDefault) {
      const existingVariants = await ctx.db
        .query("productVariants")
        .withIndex("by_product", (q) => q.eq("productId", variant.productId))
        .collect();

      for (const existingVariant of existingVariants) {
        if (existingVariant.isDefault && existingVariant._id !== variantId) {
          await ctx.db.patch(existingVariant._id, { isDefault: false });
        }
      }
    }

    await ctx.db.patch(variantId, {
      ...updates,
      updatedBy: userId,
    });

    return variantId;
  },
});

// Mutation to delete a product variant
export const deleteVariant = mutation({
  args: { variantId: v.id("productVariants") },
  handler: async (ctx, { variantId }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_DELETE);
    
    const variant = await ctx.db.get(variantId);
    if (!variant) {
      throw new Error("Variant not found");
    }

    // Don't allow deleting the last variant if it's the default
    if (variant.isDefault) {
      const otherVariants = await ctx.db
        .query("productVariants")
        .withIndex("by_product", (q) => q.eq("productId", variant.productId))
        .collect();

      if (otherVariants.length > 1) {
        // Set another variant as default
        const nextDefault = otherVariants.find(v => v._id !== variantId);
        if (nextDefault) {
          await ctx.db.patch(nextDefault._id, { isDefault: true });
        }
      }
    }

    await ctx.db.delete(variantId);
    return { success: true };
  },
});

// Internal mutation to create variants during import
export const createVariantsForProduct = internalMutation({
  args: {
    productId: v.id("products"),
    variants: v.array(v.object({
      variantName: v.string(),
      variantType: v.string(),
      priceInYuan: v.number(),
      serviceFee: v.number(),
      finalPrice: v.number(),
      stockCount: v.number(),
      availableQuantity: v.optional(v.number()),
      minQuantity: v.optional(v.number()),
      specifications: v.optional(v.object({
        weight: v.optional(v.object({
          value: v.number(),
          unit: v.string(),
        })),
        customAttributes: v.optional(v.object({})),
      })),
      pricingTiers: v.optional(v.array(v.object({
        minQuantity: v.number(),
        pricePerUnit: v.number(),
        currency: v.string(),
        description: v.optional(v.string()),
      }))),
      status: v.union(v.literal("active"), v.literal("inactive"), v.literal("out_of_stock")),
      isDefault: v.boolean(),
      sourceMetadata: v.optional(v.object({
        originalVariantId: v.optional(v.string()),
        lastSyncedAt: v.optional(v.number()),
      })),
    })),
    createdBy: v.id("users"),
  },
  handler: async (ctx, { productId, variants, createdBy }) => {
    const variantIds = [];

    for (const variantData of variants) {
      const variantId = await ctx.db.insert("productVariants", {
        productId,
        ...variantData,
        createdBy,
        updatedBy: createdBy,
      });
      variantIds.push(variantId);
    }

    return variantIds;
  },
});

// Query to get variant statistics for analytics
export const getVariantStats = query({
  args: { productId: v.optional(v.id("products")) },
  handler: async (ctx, { productId }) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    let variants;
    if (productId) {
      variants = await ctx.db
        .query("productVariants")
        .withIndex("by_product", (q) => q.eq("productId", productId))
        .collect();
    } else {
      variants = await ctx.db.query("productVariants").collect();
    }

    const stats = {
      total: variants.length,
      active: variants.filter(v => v.status === "active").length,
      outOfStock: variants.filter(v => v.status === "out_of_stock").length,
      inactive: variants.filter(v => v.status === "inactive").length,
      totalStock: variants.reduce((sum, v) => sum + v.stockCount, 0),
      variantTypes: {} as Record<string, number>,
      averagePrice: variants.length > 0 
        ? variants.reduce((sum, v) => sum + v.finalPrice, 0) / variants.length 
        : 0,
    };

    // Count variants by type
    variants.forEach(variant => {
      stats.variantTypes[variant.variantType] = (stats.variantTypes[variant.variantType] || 0) + 1;
    });

    return stats;
  },
});
