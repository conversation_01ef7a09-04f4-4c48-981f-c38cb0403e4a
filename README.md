# Convex Authentication Setup

This document outlines the steps to generate the necessary environment variables (`JWT_PRIVATE_KEY` and `J<PERSON><PERSON>`) for Convex authentication, resolving common errors like "pkcs8" must be PKCS#8 formatted string and "Missing environment variable JWKS".

## Prerequisites

*   `openssl` installed on your system.
*   Node.js and `npm` (or `yarn`) for installing `jose`.

## Steps to Generate Keys and JWKS

### 1. Generate RSA Private Key

First, generate an RSA private key in PEM format. This key will be used to derive your PKCS#8 key.

```bash
openssl genpkey -algorithm RSA -outform PEM -out private_key.pem -pkeyopt rsa_keygen_bits:2048
```

### 2. Convert to PKCS#8 Format

Convert the generated `private_key.pem` into the PKCS#8 format, which is required by the Convex authentication system.

```bash
openssl pkcs8 -topk8 -inform PEM -outform PEM -nocrypt -in private_key.pem -out private_key_pkcs8.pem
```

### 3. Update `.env.local` with `JWT_PRIVATE_KEY`

Copy the entire content of `private_key_pkcs8.pem` (including `-----BEGIN PRIVATE KEY-----` and `-----END PRIVATE KEY-----` lines) and replace the placeholder value for `JWT_PRIVATE_KEY` in your `.env.local` file. Ensure the key is enclosed in double quotes.

Example:
```
JWT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQ...
-----END PRIVATE KEY-----"
```

### 4. Install `jose` Library

If you haven't already, install the `jose` library, which is used to generate the JWKS.

```bash
npm install jose
# or
yarn add jose
```

### 5. Add `"type": "module"` to `package.json`

To ensure the JWKS generation script runs correctly as an ES module, add `"type": "module"` to your `package.json` file.

```json
{
  "name": "your-app",
  "version": "1.0.0",
  "type": "module", // Add this line
  "dependencies": {
    "jose": "^5.x.x",
    // ... other dependencies
  }
}
```

### 6. Create and Run `generate_jwks.js`

Create a file named `generate_jwks.js` in your project's root directory with the following content:

```javascript
import { importPKCS8, exportJWK } from 'jose';
import { readFileSync } from 'fs';

async function generateJwks() {
  const privateKeyPem = readFileSync('private_key_pkcs8.pem', 'utf8');
  // Import the private key, explicitly setting extractable to true
  const privateKey = await importPKCS8(privateKeyPem, 'RS256', { extractable: true });

  // Export the public key part of the private key as JWK
  const publicKeyJwk = await exportJWK(privateKey);

  const jwks = {
    keys: [{
      ...publicKeyJwk,
      alg: 'RS256', // Algorithm used for signing
      use: 'sig',   // Use for signing
      kid: 'my-auth-key' // Key ID - can be any unique string
    }]
  };
  console.log(JSON.stringify(jwks, null, 2));
}

generateJwks().catch(console.error);
```

Then, run the script from your terminal:

```bash
node generate_jwks.js
```

This will output the JWKS JSON to your console.

### 7. Update `.env.local` with `JWKS`

Copy the entire JSON output from the previous step and add it as the `JWKS` environment variable in your `.env.local` file. Ensure the entire JSON string is enclosed in double quotes.

Example:
```
JWKS="{\"keys\":[{\"kty\":\"RSA\",\"n\":\"...\",\"e\":\"AQAB\",\"alg\":\"RS256\",\"use\":\"sig\",\"kid\":\"my-auth-key\"}]}"
```
(The actual `n` value will be much longer.)

### 8. Restart Convex Development Server

After updating the `.env.local` file with both `JWT_PRIVATE_KEY` and `JWKS`, restart your Convex development server for the changes to take effect.
