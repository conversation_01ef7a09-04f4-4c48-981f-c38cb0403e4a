#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to promote the first user to super admin
 * This script should be run when setting up the application for the first time
 *
 * Usage: node scripts/make-super-admin.js <user-email>
 *
 * Requirements:
 * - The user must already exist in the system (created through authentication)
 * - No super admin should exist yet
 * - The user must be currently signed in (or provide their email)
 */

import { ConvexHttpClient } from "convex/browser";
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

async function main() {
  const args = process.argv.slice(2);
  const userEmail = args[0];

  if (!userEmail) {
    console.error('Usage: node scripts/make-super-admin.js <user-email>');
    console.error('Example: node scripts/make-super-admin.js <EMAIL>');
    process.exit(1);
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(userEmail)) {
    console.error('Please provide a valid email address');
    process.exit(1);
  }

  const convexUrl = process.env.NEXT_PUBLIC_CONVEX_URL;
  if (!convexUrl) {
    console.error('NEXT_PUBLIC_CONVEX_URL environment variable is not set');
    console.error('Please check your .env.local file');
    process.exit(1);
  }

  console.log('🚀 Connecting to Convex...');
  const client = new ConvexHttpClient(convexUrl);

  try {
    // Check if super admin already exists
    console.log('🔍 Checking for existing super admin...');
    const setupStatus = await client.query('auth/setup:getSetupStatus');

    if (setupStatus.hasSuperAdmin) {
      console.log('❌ Super admin already exists!');
      console.log(`   Total users: ${setupStatus.totalUsers}`);
      console.log(`   Total admins: ${setupStatus.totalAdmins}`);
      console.log('   This script can only be used when no super admin exists.');
      process.exit(1);
    }

    console.log('✅ No super admin found. Proceeding with setup...');

    // Create the first super admin
    console.log(`👑 Creating first super admin for: ${userEmail}`);
    const result = await client.mutation('auth/setup:createFirstSuperAdmin', {
      email: userEmail,
      password: '', // Not needed for existing users
      name: '' // Not needed for existing users
    });

    if (result.success) {
      console.log('🎉 Success! First super admin created successfully!');
      console.log(`   Admin User ID: ${result.adminUserId}`);
      console.log(`   Message: ${result.message}`);
      console.log('');
      console.log('📋 Summary:');
      console.log(`   - Email: ${userEmail}`);
      console.log('   - Role: super_admin');
      console.log('   - All permissions granted');
      console.log('');
      console.log('🔐 You can now access the admin dashboard at /admin');
      console.log('📖 Visit /admin/users to manage other admin users');
    } else {
      console.error('❌ Failed to create super admin');
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);

    if (error.message.includes('User with email') && error.message.includes('must be created first')) {
      console.log('');
      console.log('💡 Solution:');
      console.log('   1. Make sure you have signed up/in with this email address');
      console.log('   2. Visit your app and complete the authentication process');
      console.log('   3. Then run this script again');
    } else if (error.message.includes('Super admin already exists')) {
      console.log('   A super admin has been created by another process');
    }

    process.exit(1);
  }
}

// Handle script execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
}

export { main };